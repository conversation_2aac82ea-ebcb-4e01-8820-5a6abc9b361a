/*******************************************************************************
 * Size: 12 px
 * Bpp: 4
 * Opts: --lcd --no-compress --no-prefilter --bpp 4 --size 12 --font <PERSON>-Medium.ttf -r 0x20-0x7F,0xB0,0x2022 --font FontAwesome5-Solid+Brands+Regular.woff -r 61441,61448,61451,61452,61452,61453,61457,61459,61461,61465,61468,61473,61478,61479,61480,61502,61507,61512,61515,61516,61517,61521,61522,61523,61524,61543,61544,61550,61552,61553,61556,61559,61560,61561,61563,61587,61589,61636,61637,61639,61641,61664,61671,61674,61683,61724,61732,61787,61931,62016,62017,62018,62019,62020,62087,62099,62212,62189,62810,63426,63650 --format lvgl -o lv_font_montserrat_12_subpx.c --force-fast-kern-format
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
    #include "lvgl.h"
#else
    #include "../../lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRAT_12_SUBPX
    #define LV_FONT_MONTSERRAT_12_SUBPX 1
#endif

#if LV_FONT_MONTSERRAT_12_SUBPX

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x0, 0x6b, 0xff, 0x94, 0x0, 0x5, 0xaf, 0xe9,
    0x30, 0x0, 0x4a, 0xfd, 0x83, 0x0, 0x4, 0x9f,
    0xd7, 0x20, 0x0, 0x39, 0xec, 0x72, 0x0, 0x3,
    0x8d, 0xc6, 0x10, 0x0, 0x2, 0x33, 0x10, 0x0,
    0x2, 0x68, 0x84, 0x10, 0x1, 0x5a, 0xed, 0x83,
    0x0,

    /* U+0022 "\"" */
    0x3, 0x9e, 0xc6, 0x11, 0x6b, 0xe9, 0x40, 0x0,
    0x38, 0xeb, 0x60, 0x6, 0xbe, 0x93, 0x0, 0x3,
    0x8d, 0xb6, 0x0, 0x5b, 0xe8, 0x30, 0x0, 0x14,
    0x75, 0x30, 0x2, 0x57, 0x41, 0x0,

    /* U+0023 "#" */
    0x0, 0x0, 0x0, 0x4, 0xad, 0x83, 0x0, 0x0,
    0x38, 0xda, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x6c, 0xb6, 0x10, 0x0, 0x5, 0xad, 0x83,
    0x0, 0x0, 0x0, 0x4, 0x9e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x61,
    0x0, 0x0, 0x0, 0x5, 0xad, 0x82, 0x0, 0x0,
    0x38, 0xd9, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x6c, 0xc6, 0x10, 0x0, 0x5, 0xad, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x28, 0xda,
    0x50, 0x0, 0x1, 0x6b, 0xc7, 0x10, 0x0, 0x0,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb6, 0x0, 0x0, 0x0,
    0x1, 0x6c, 0xc6, 0x10, 0x0, 0x4, 0xad, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0xda,
    0x40, 0x0, 0x1, 0x6c, 0xc6, 0x10, 0x0, 0x0,
    0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9c, 0x82, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x9c, 0x82, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x37, 0xac, 0xde, 0xff, 0xff, 0xed,
    0xb9, 0x63, 0x10, 0x0, 0x1, 0x5b, 0xfe, 0xb7,
    0x46, 0xac, 0x95, 0x35, 0x79, 0x74, 0x0, 0x0,
    0x4, 0x9e, 0xe9, 0x40, 0x4, 0x9c, 0x82, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x5b, 0xfe, 0xc9,
    0x67, 0xbc, 0x82, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x26, 0x9b, 0xde, 0xff, 0xfe, 0xca,
    0x85, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x9c, 0xa7, 0x7a, 0xdf, 0xea, 0x51, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9c, 0x82, 0x0,
    0x49, 0xfe, 0x93, 0x0, 0x5, 0xad, 0xb8, 0x54,
    0x25, 0xac, 0x95, 0x47, 0xbe, 0xfa, 0x51, 0x0,
    0x0, 0x26, 0x8b, 0xce, 0xff, 0xff, 0xff, 0xec,
    0xa7, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x9c, 0x82, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x46, 0x41, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x26, 0xac, 0xcc, 0xcb, 0x84, 0x10, 0x0,
    0x0, 0x0, 0x38, 0xcb, 0x61, 0x0, 0x0, 0x16,
    0xbb, 0x61, 0x0, 0x4, 0xac, 0x82, 0x0, 0x0,
    0x27, 0xcb, 0x62, 0x0, 0x0, 0x0, 0x38, 0xc9,
    0x30, 0x0, 0x1, 0x7c, 0xa4, 0x0, 0x27, 0xcc,
    0x72, 0x0, 0x0, 0x0, 0x0, 0x5, 0xac, 0x72,
    0x0, 0x15, 0xab, 0x72, 0x26, 0xbc, 0x72, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x25, 0x9b, 0xcc,
    0xba, 0x63, 0x26, 0xbc, 0x83, 0x25, 0x9b, 0xcc,
    0xb9, 0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x15, 0xbc, 0x83, 0x15, 0xbc, 0x72, 0x0, 0x16,
    0xbb, 0x72, 0x0, 0x0, 0x0, 0x0, 0x15, 0xac,
    0x94, 0x0, 0x38, 0xc9, 0x30, 0x0, 0x2, 0x7c,
    0xa4, 0x0, 0x0, 0x0, 0x14, 0xac, 0x94, 0x0,
    0x0, 0x16, 0xbb, 0x61, 0x0, 0x4, 0x9c, 0x72,
    0x0, 0x0, 0x4, 0x9c, 0xa4, 0x10, 0x0, 0x0,
    0x0, 0x37, 0xab, 0xba, 0xbb, 0x84, 0x10,

    /* U+0026 "&" */
    0x0, 0x0, 0x2, 0x59, 0xce, 0xee, 0xed, 0xc9,
    0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xaf, 0xc8, 0x20, 0x0, 0x38, 0xdd, 0x82, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4a, 0xfc, 0x72,
    0x0, 0x15, 0xae, 0xc6, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x37, 0xce, 0xdb, 0xcd, 0xda,
    0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x37, 0xbd, 0xdd, 0xef, 0xc8, 0x30, 0x0, 0x1,
    0x12, 0x0, 0x0, 0x0, 0x16, 0xce, 0xc7, 0x30,
    0x1, 0x5a, 0xde, 0xb7, 0x23, 0x8d, 0xd8, 0x30,
    0x0, 0x16, 0xbf, 0xb6, 0x0, 0x0, 0x0, 0x1,
    0x5a, 0xde, 0xff, 0xd7, 0x20, 0x0, 0x0, 0x49,
    0xef, 0xc8, 0x42, 0x11, 0x12, 0x36, 0x9c, 0xef,
    0xfe, 0xb6, 0x20, 0x0, 0x0, 0x3, 0x69, 0xcd,
    0xef, 0xff, 0xed, 0xc9, 0x63, 0x12, 0x6b, 0xb7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0x3, 0x9e, 0xc6, 0x10, 0x0, 0x38, 0xeb, 0x60,
    0x0, 0x3, 0x8d, 0xb6, 0x0, 0x0, 0x14, 0x75,
    0x30, 0x0,

    /* U+0028 "(" */
    0x0, 0x0, 0x0, 0x5a, 0xec, 0x72, 0x0, 0x0,
    0x16, 0xbf, 0xb6, 0x10, 0x0, 0x0, 0x5b, 0xfc,
    0x61, 0x0, 0x0, 0x4, 0x9e, 0xd8, 0x30, 0x0,
    0x0, 0x16, 0xbf, 0xb6, 0x0, 0x0, 0x0, 0x27,
    0xcf, 0xa4, 0x0, 0x0, 0x0, 0x28, 0xdf, 0x94,
    0x0, 0x0, 0x0, 0x27, 0xcf, 0xa4, 0x0, 0x0,
    0x0, 0x16, 0xbf, 0xb6, 0x0, 0x0, 0x0, 0x4,
    0x9e, 0xd8, 0x30, 0x0, 0x0, 0x0, 0x5a, 0xfb,
    0x61, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xb5, 0x10,
    0x0, 0x0, 0x0, 0x5a, 0xec, 0x72,

    /* U+0029 ")" */
    0x16, 0xbe, 0xb5, 0x10, 0x0, 0x0, 0x0, 0x5a,
    0xfc, 0x72, 0x0, 0x0, 0x0, 0x6, 0xbf, 0xc6,
    0x10, 0x0, 0x0, 0x2, 0x7d, 0xfa, 0x40, 0x0,
    0x0, 0x0, 0x5a, 0xfc, 0x71, 0x0, 0x0, 0x0,
    0x49, 0xfd, 0x83, 0x0, 0x0, 0x0, 0x38, 0xee,
    0x93, 0x0, 0x0, 0x0, 0x49, 0xfd, 0x83, 0x0,
    0x0, 0x0, 0x5a, 0xfc, 0x71, 0x0, 0x0, 0x2,
    0x7d, 0xfa, 0x40, 0x0, 0x0, 0x6, 0xbf, 0xb6,
    0x10, 0x0, 0x0, 0x5a, 0xfc, 0x71, 0x0, 0x0,
    0x16, 0xbe, 0xb5, 0x10, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0x0, 0x0, 0x4a, 0xb6, 0x10, 0x0,
    0x0, 0x0, 0x3, 0x8c, 0xc9, 0x9c, 0xca, 0x9b,
    0xca, 0x51, 0x0, 0x0, 0x2, 0x6b, 0xef, 0xff,
    0xd8, 0x30, 0x0, 0x0, 0x3, 0x8b, 0xa7, 0x8b,
    0xc9, 0x79, 0xba, 0x51, 0x0, 0x0, 0x0, 0x0,
    0x38, 0xa6, 0x10, 0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x0, 0x37, 0xb7, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbf,
    0xa5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xfa, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x27, 0xce, 0xee, 0xef, 0xff, 0xff, 0xee, 0xee,
    0xc7, 0x20, 0x0, 0x1, 0x11, 0x11, 0x6b, 0xfb,
    0x61, 0x11, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xbf, 0xa5, 0x0, 0x0, 0x0, 0x0,

    /* U+002C "," */
    0x1, 0x48, 0x85, 0x20, 0x0, 0x4a, 0xff, 0xc6,
    0x10, 0x0, 0x5b, 0xd8, 0x20, 0x0, 0x39, 0xc8,
    0x30, 0x0,

    /* U+002D "-" */
    0x4, 0x9f, 0xff, 0xff, 0xff, 0xd8, 0x30, 0x0,
    0x1, 0x22, 0x22, 0x22, 0x22, 0x10, 0x0,

    /* U+002E "." */
    0x2, 0x6a, 0xa7, 0x30, 0x0, 0x49, 0xdd, 0x94,
    0x0,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x67,
    0x42, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5b,
    0xfa, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xaf, 0xb5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5a, 0xfb, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xae, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4a, 0xec, 0x61, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x9e, 0xc6, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x49, 0xec, 0x71, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x9e, 0xc7, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x39, 0xed, 0x72, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x8e, 0xd7, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x38, 0xdd, 0x82, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x8d, 0xd8, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x2, 0x59, 0xbd, 0xef, 0xfe, 0xdb,
    0x95, 0x20, 0x0, 0x0, 0x0, 0x4, 0xae, 0xfd,
    0xa6, 0x43, 0x34, 0x6a, 0xdf, 0xea, 0x40, 0x0,
    0x1, 0x6c, 0xfd, 0x82, 0x0, 0x0, 0x0, 0x0,
    0x27, 0xdf, 0xc6, 0x10, 0x5, 0xaf, 0xe8, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x8d, 0xfa, 0x50,
    0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x7c, 0xfb, 0x61, 0x5, 0xaf, 0xe8, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x8d, 0xfa, 0x50,
    0x1, 0x6c, 0xfd, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x27, 0xdf, 0xc6, 0x10, 0x0, 0x4, 0xae, 0xfd,
    0xa6, 0x43, 0x34, 0x6a, 0xdf, 0xea, 0x40, 0x0,
    0x0, 0x0, 0x2, 0x59, 0xbd, 0xef, 0xfe, 0xdb,
    0x95, 0x20, 0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x39, 0xef, 0xff, 0xff, 0xfd, 0x83, 0x0,
    0x0, 0x12, 0x22, 0x26, 0xbf, 0xd8, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x5b, 0xfd, 0x83, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xbf, 0xd8, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x5b, 0xfd, 0x83, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xbf, 0xd8, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xfd, 0x83, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xbf, 0xd8, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x5b, 0xfd, 0x83, 0x0,

    /* U+0032 "2" */
    0x0, 0x0, 0x13, 0x79, 0xbd, 0xef, 0xff, 0xed,
    0xc9, 0x62, 0x0, 0x0, 0x0, 0x3, 0x8d, 0xeb,
    0x85, 0x43, 0x33, 0x47, 0xad, 0xfe, 0x83, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x27, 0xcf, 0xc6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5a, 0xfe, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x5a,
    0xee, 0xb5, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x15, 0xad, 0xec, 0x73, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x5a, 0xde, 0xc8, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0xad,
    0xfd, 0xa6, 0x32, 0x22, 0x22, 0x22, 0x21, 0x10,
    0x0, 0x3, 0x8e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x72,

    /* U+0033 "3" */
    0x0, 0x3, 0x9e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa4, 0x0, 0x0, 0x0, 0x12, 0x22,
    0x22, 0x22, 0x22, 0x49, 0xdf, 0xc8, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xde,
    0xb6, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x5a, 0xef, 0xc7, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x7b, 0xcc, 0xde,
    0xfe, 0xc8, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x27, 0xcf, 0xd7, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x9e, 0xf9, 0x40, 0x0, 0x16, 0xbd, 0xc9,
    0x65, 0x43, 0x33, 0x46, 0x9c, 0xff, 0xa5, 0x10,
    0x0, 0x1, 0x36, 0x9b, 0xce, 0xff, 0xff, 0xed,
    0xca, 0x63, 0x10, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xde,
    0xb6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x14, 0x9e, 0xeb, 0x51, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x4a,
    0xee, 0xa5, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x15, 0xae, 0xea, 0x51, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x5a, 0xee, 0xa4, 0x10, 0x0, 0x28, 0xdf, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x15, 0xbe, 0xea, 0x41,
    0x0, 0x0, 0x2, 0x8d, 0xf9, 0x40, 0x0, 0x0,
    0x0, 0x38, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd8, 0x30, 0x0, 0x12,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x25, 0x9e, 0xfa,
    0x62, 0x22, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x38, 0xef, 0x94, 0x0, 0x0,
    0x0, 0x0,

    /* U+0035 "5" */
    0x0, 0x0, 0x1, 0x7c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0x0, 0x0, 0x0, 0x3, 0x9e,
    0xe9, 0x52, 0x22, 0x22, 0x22, 0x22, 0x10, 0x0,
    0x0, 0x0, 0x5, 0xaf, 0xc6, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x17, 0xcf,
    0xff, 0xff, 0xee, 0xdd, 0xb9, 0x63, 0x10, 0x0,
    0x0, 0x0, 0x1, 0x22, 0x22, 0x22, 0x33, 0x46,
    0x9c, 0xff, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x8d, 0xfb, 0x60,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x8d, 0xfb, 0x61, 0x0, 0x4, 0x9e, 0xeb,
    0x86, 0x43, 0x33, 0x45, 0x8c, 0xef, 0xb6, 0x10,
    0x0, 0x0, 0x24, 0x8a, 0xcd, 0xef, 0xff, 0xfe,
    0xca, 0x74, 0x10, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x1, 0x36, 0x9b, 0xde, 0xff, 0xfe,
    0xdc, 0x95, 0x10, 0x0, 0x0, 0x3, 0x8d, 0xfd,
    0xa7, 0x53, 0x32, 0x22, 0x34, 0x32, 0x0, 0x0,
    0x1, 0x6b, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xaf, 0xd9, 0x66,
    0x9b, 0xde, 0xee, 0xdc, 0xa7, 0x31, 0x0, 0x0,
    0x16, 0xbf, 0xff, 0xec, 0x95, 0x43, 0x23, 0x58,
    0xce, 0xfa, 0x51, 0x0, 0x5, 0xaf, 0xfd, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x28, 0xdf, 0xb5, 0x0,
    0x2, 0x7d, 0xfd, 0x82, 0x0, 0x0, 0x0, 0x0,
    0x27, 0xdf, 0xa5, 0x0, 0x0, 0x15, 0xaf, 0xec,
    0x84, 0x32, 0x12, 0x47, 0xbe, 0xea, 0x51, 0x0,
    0x0, 0x0, 0x13, 0x7a, 0xce, 0xff, 0xff, 0xec,
    0xa7, 0x31, 0x0, 0x0,

    /* U+0037 "7" */
    0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x50, 0x0, 0x5a, 0xfd, 0x94, 0x22,
    0x22, 0x22, 0x22, 0x49, 0xdf, 0xc7, 0x20, 0x0,
    0x37, 0xb9, 0x62, 0x0, 0x0, 0x0, 0x3, 0x8e,
    0xfb, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5a, 0xee, 0x94, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xd8,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x7d, 0xfc, 0x71, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x39, 0xef, 0xa5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xaf, 0xe9, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x6c, 0xfd, 0x82, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x0, 0x36, 0x9c, 0xde, 0xff, 0xfe, 0xdc,
    0xa7, 0x31, 0x0, 0x0, 0x0, 0x49, 0xef, 0xc8,
    0x53, 0x11, 0x12, 0x48, 0xcf, 0xea, 0x50, 0x0,
    0x2, 0x7d, 0xfb, 0x61, 0x0, 0x0, 0x0, 0x0,
    0x5a, 0xfd, 0x83, 0x0, 0x0, 0x39, 0xef, 0xb7,
    0x31, 0x0, 0x1, 0x37, 0xbe, 0xe9, 0x40, 0x0,
    0x0, 0x2, 0x6b, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x62, 0x0, 0x0, 0x3, 0x8d, 0xfd, 0x95,
    0x21, 0x0, 0x1, 0x24, 0x8c, 0xfe, 0x94, 0x0,
    0x16, 0xcf, 0xc7, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xbf, 0xd7, 0x20, 0x3, 0x8d, 0xfd, 0xa6,
    0x32, 0x11, 0x12, 0x35, 0x9c, 0xfe, 0x94, 0x0,
    0x0, 0x2, 0x58, 0xbc, 0xef, 0xff, 0xff, 0xed,
    0xb9, 0x52, 0x0, 0x0,

    /* U+0039 "9" */
    0x0, 0x13, 0x7a, 0xce, 0xff, 0xff, 0xec, 0xa7,
    0x31, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xeb, 0x63,
    0x21, 0x12, 0x35, 0x9d, 0xeb, 0x61, 0x0, 0x0,
    0x49, 0xfe, 0x83, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xbf, 0xd8, 0x30, 0x0, 0x27, 0xcf, 0xd9, 0x42,
    0x0, 0x0, 0x13, 0x7c, 0xff, 0xfb, 0x60, 0x0,
    0x0, 0x26, 0xad, 0xff, 0xff, 0xff, 0xfd, 0xa7,
    0x9c, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x11, 0x10, 0x0, 0x3, 0x8d, 0xfb, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38,
    0xdf, 0xc7, 0x20, 0x0, 0x0, 0x24, 0x65, 0x32,
    0x23, 0x45, 0x8b, 0xef, 0xd9, 0x40, 0x0, 0x0,
    0x1, 0x48, 0xcd, 0xef, 0xff, 0xed, 0xca, 0x74,
    0x10, 0x0, 0x0, 0x0,

    /* U+003A ":" */
    0x4, 0x9e, 0xe9, 0x40, 0x0, 0x26, 0xaa, 0x73,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x26,
    0xaa, 0x73, 0x0, 0x4, 0x9d, 0xd9, 0x40, 0x0,

    /* U+003B ";" */
    0x4, 0x9e, 0xe9, 0x40, 0x0, 0x26, 0xaa, 0x73,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14,
    0x88, 0x52, 0x0, 0x4, 0xaf, 0xfc, 0x61, 0x0,
    0x5, 0xbd, 0x82, 0x0, 0x3, 0x9c, 0x83, 0x0,
    0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x23, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x24,
    0x79, 0xbd, 0xee, 0xb6, 0x10, 0x0, 0x24, 0x79,
    0xbd, 0xee, 0xc9, 0x75, 0x31, 0x0, 0x0, 0x0,
    0x38, 0xdf, 0xec, 0x84, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x46, 0x9b, 0xde, 0xec,
    0xa7, 0x53, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x46, 0x9b, 0xde, 0xc7, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x10, 0x0,

    /* U+003D "=" */
    0x3, 0x8e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x27, 0xce, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xc7, 0x20, 0x0, 0x1, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x0, 0x0,

    /* U+003E ">" */
    0x1, 0x23, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x26, 0xbe, 0xed, 0xb9, 0x64,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x35, 0x7a, 0xce, 0xed, 0xb9, 0x64, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x8c, 0xef,
    0xd8, 0x20, 0x0, 0x0, 0x13, 0x57, 0xac, 0xee,
    0xdb, 0x96, 0x42, 0x0, 0x0, 0x27, 0xce, 0xdb,
    0x96, 0x42, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x0, 0x14, 0x7a, 0xcd, 0xef, 0xff, 0xed,
    0xca, 0x63, 0x0, 0x0, 0x0, 0x4, 0x9d, 0xda,
    0x75, 0x32, 0x22, 0x36, 0xad, 0xfe, 0x83, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x38, 0xef, 0xa5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x15, 0xae, 0xea, 0x51, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x49, 0xde,
    0xc8, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x27, 0xdf, 0xc7, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x25, 0x88, 0x52, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xdd, 0x94,
    0x0, 0x0, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x47, 0x9b, 0xcd,
    0xdd, 0xdd, 0xdd, 0xdc, 0xa8, 0x63, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0x9c,
    0xdb, 0x85, 0x31, 0x0, 0x0, 0x0, 0x0, 0x13,
    0x68, 0xcd, 0xc7, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x7c, 0xda, 0x51, 0x1, 0x48, 0xbd, 0xef,
    0xfe, 0xdb, 0x86, 0x9d, 0xe9, 0x56, 0xbd, 0xa4,
    0x10, 0x0, 0x0, 0x4, 0x9d, 0xa5, 0x0, 0x27,
    0xcf, 0xd9, 0x52, 0x10, 0x12, 0x59, 0xdf, 0xfe,
    0x83, 0x1, 0x7c, 0xc7, 0x10, 0x0, 0x4, 0x9d,
    0xa5, 0x0, 0x28, 0xde, 0x94, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xaf, 0xe8, 0x30, 0x1, 0x7c, 0xb6,
    0x0, 0x0, 0x5b, 0xd8, 0x20, 0x4, 0x9f, 0xd7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x27, 0xde, 0x83,
    0x0, 0x5, 0xac, 0x72, 0x0, 0x5, 0xbd, 0x82,
    0x0, 0x28, 0xde, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xaf, 0xe8, 0x30, 0x1, 0x6b, 0xb6, 0x0,
    0x0, 0x39, 0xda, 0x50, 0x0, 0x27, 0xcf, 0xd9,
    0x52, 0x10, 0x12, 0x6a, 0xdf, 0xff, 0xa5, 0x12,
    0x7b, 0xc7, 0x20, 0x0, 0x0, 0x49, 0xda, 0x50,
    0x0, 0x1, 0x48, 0xbd, 0xef, 0xfe, 0xdb, 0x84,
    0x35, 0xad, 0xff, 0xeb, 0x73, 0x0, 0x0, 0x0,
    0x0, 0x26, 0xbd, 0xa5, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x59, 0xcd,
    0xb8, 0x53, 0x10, 0x0, 0x0, 0x0, 0x24, 0x54,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x25, 0x79, 0xbd, 0xdd, 0xee,
    0xee, 0xdd, 0xb9, 0x62, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf,
    0xfd, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x28, 0xdf,
    0xdd, 0xee, 0x94, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xee,
    0x94, 0x38, 0xdf, 0xb5, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5b, 0xfe,
    0x83, 0x0, 0x27, 0xcf, 0xc7, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x7c, 0xfd,
    0x72, 0x0, 0x0, 0x16, 0xcf, 0xe8, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x8d, 0xfc,
    0x71, 0x0, 0x0, 0x0, 0x15, 0xbf, 0xfa, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xae, 0xff,
    0xff, 0xee, 0xee, 0xee, 0xee, 0xef, 0xff, 0xfb,
    0x61, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xea,
    0x52, 0x11, 0x11, 0x11, 0x11, 0x11, 0x14, 0x8d,
    0xfd, 0x72, 0x0, 0x0, 0x0, 0x27, 0xcf, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x6b, 0xfe, 0x94, 0x0, 0x0,

    /* U+0042 "B" */
    0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xdb, 0x95, 0x20, 0x0, 0x0, 0x1, 0x6b,
    0xfc, 0x72, 0x11, 0x11, 0x11, 0x12, 0x36, 0xad,
    0xfd, 0x72, 0x0, 0x0, 0x16, 0xbf, 0xc7, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x8e, 0xfa, 0x50,
    0x0, 0x1, 0x6b, 0xfc, 0x72, 0x0, 0x0, 0x0,
    0x0, 0x14, 0x8d, 0xfc, 0x72, 0x0, 0x0, 0x16,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xea, 0x51, 0x0, 0x0, 0x1, 0x6b, 0xfc, 0x83,
    0x22, 0x22, 0x22, 0x22, 0x34, 0x69, 0xdf, 0xd7,
    0x20, 0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x17, 0xcf, 0xc7, 0x10, 0x1,
    0x6b, 0xfc, 0x72, 0x11, 0x11, 0x11, 0x11, 0x13,
    0x58, 0xcf, 0xe9, 0x40, 0x0, 0x16, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xed, 0xc9, 0x63,
    0x0, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x0, 0x13, 0x69, 0xbd, 0xee, 0xff,
    0xfe, 0xdb, 0x96, 0x31, 0x0, 0x0, 0x0, 0x15,
    0x9e, 0xfe, 0xc9, 0x65, 0x33, 0x33, 0x46, 0x8b,
    0xee, 0xa4, 0x0, 0x0, 0x4a, 0xef, 0xb6, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x49, 0xfe, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf,
    0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x49, 0xfe, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4a, 0xef, 0xb6, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x15, 0xae, 0xfe, 0xc9, 0x65, 0x33, 0x33, 0x46,
    0x8b, 0xee, 0xa4, 0x0, 0x0, 0x0, 0x0, 0x13,
    0x69, 0xbd, 0xef, 0xff, 0xfe, 0xdb, 0x96, 0x31,
    0x0, 0x0,

    /* U+0044 "D" */
    0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xed, 0xca, 0x85, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x16, 0xbf, 0xc8, 0x32, 0x22, 0x22, 0x23, 0x35,
    0x69, 0xce, 0xfd, 0x94, 0x10, 0x0, 0x0, 0x16,
    0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x7c, 0xfe, 0x93, 0x0, 0x0, 0x16, 0xbf,
    0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xbf, 0xe8, 0x30, 0x0, 0x16, 0xbf, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x8e, 0xfa, 0x50, 0x0, 0x16, 0xbf, 0xc7, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xaf,
    0xe8, 0x30, 0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x7c, 0xfe, 0x93,
    0x0, 0x0, 0x16, 0xbf, 0xc8, 0x32, 0x22, 0x22,
    0x23, 0x35, 0x69, 0xce, 0xfd, 0x94, 0x10, 0x0,
    0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xed, 0xcb, 0x86, 0x31, 0x0, 0x0, 0x0,

    /* U+0045 "E" */
    0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd8, 0x30, 0x0, 0x16, 0xbf, 0xc8,
    0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x11, 0x0,
    0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x50, 0x0, 0x0, 0x16, 0xbf, 0xc8,
    0x32, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0x0,
    0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc8,
    0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0,
    0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x50,

    /* U+0046 "F" */
    0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd8, 0x30, 0x0, 0x16, 0xbf, 0xc8,
    0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x11, 0x0,
    0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x50, 0x0, 0x0, 0x16, 0xbf, 0xd8,
    0x42, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0x0,
    0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x0, 0x13, 0x69, 0xbc, 0xee, 0xff,
    0xfe, 0xdc, 0xa7, 0x41, 0x0, 0x0, 0x0, 0x15,
    0xae, 0xfe, 0xc9, 0x65, 0x43, 0x33, 0x46, 0x8a,
    0xde, 0xb5, 0x10, 0x0, 0x4a, 0xef, 0xc7, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x49, 0xfe, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf,
    0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x25, 0x87, 0x42, 0x0, 0x49, 0xfe, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x9e, 0xe9,
    0x30, 0x0, 0x4a, 0xef, 0xb6, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x49, 0xee, 0x93, 0x0, 0x0,
    0x15, 0xae, 0xfe, 0xc9, 0x65, 0x33, 0x33, 0x45,
    0x7a, 0xdf, 0xe8, 0x30, 0x0, 0x0, 0x0, 0x13,
    0x69, 0xbd, 0xee, 0xff, 0xfe, 0xdc, 0xa7, 0x41,
    0x0, 0x0,

    /* U+0048 "H" */
    0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xbf, 0xd7, 0x20, 0x1, 0x6b,
    0xfc, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6b, 0xfd, 0x72, 0x0, 0x16, 0xbf, 0xc7, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xbf, 0xd7,
    0x20, 0x1, 0x6b, 0xfc, 0x72, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6b, 0xfd, 0x72, 0x0, 0x16,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd7, 0x20, 0x1, 0x6b, 0xfd, 0x84,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x23, 0x7c, 0xfd,
    0x72, 0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xbf, 0xd7, 0x20, 0x1,
    0x6b, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6b, 0xfd, 0x72, 0x0, 0x16, 0xbf, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xbf,
    0xd7, 0x20,

    /* U+0049 "I" */
    0x0, 0x16, 0xbf, 0xc7, 0x20, 0x1, 0x6b, 0xfc,
    0x72, 0x0, 0x16, 0xbf, 0xc7, 0x20, 0x1, 0x6b,
    0xfc, 0x72, 0x0, 0x16, 0xbf, 0xc7, 0x20, 0x1,
    0x6b, 0xfc, 0x72, 0x0, 0x16, 0xbf, 0xc7, 0x20,
    0x1, 0x6b, 0xfc, 0x72, 0x0, 0x16, 0xbf, 0xc7,
    0x20,

    /* U+004A "J" */
    0x0, 0x0, 0x49, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x40, 0x0, 0x0, 0x0, 0x12, 0x22, 0x22,
    0x22, 0x59, 0xef, 0xa4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x8e, 0xfa, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0xef,
    0xa4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x8e, 0xfa, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x38, 0xef, 0xa4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x9f, 0xe9,
    0x30, 0x0, 0x3, 0x8d, 0xc9, 0x53, 0x22, 0x47,
    0xbe, 0xfa, 0x51, 0x0, 0x0, 0x2, 0x58, 0xbd,
    0xef, 0xfe, 0xdb, 0x95, 0x20, 0x0, 0x0,

    /* U+004B "K" */
    0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x27, 0xbe, 0xd9, 0x41, 0x0, 0x1, 0x6b,
    0xfc, 0x72, 0x0, 0x0, 0x0, 0x15, 0xae, 0xeb,
    0x62, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc7, 0x20,
    0x0, 0x14, 0x9d, 0xec, 0x73, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x6b, 0xfc, 0x72, 0x3, 0x7c, 0xed,
    0x94, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x16,
    0xbf, 0xc9, 0x8b, 0xef, 0xff, 0xc7, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xff, 0xff,
    0xc8, 0x45, 0x9d, 0xfd, 0x94, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x16, 0xbf, 0xea, 0x51, 0x0, 0x0,
    0x27, 0xcf, 0xea, 0x51, 0x0, 0x0, 0x0, 0x1,
    0x6b, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x1, 0x5a,
    0xef, 0xc7, 0x20, 0x0, 0x0, 0x16, 0xbf, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x3, 0x8d, 0xfd,
    0x83, 0x0,

    /* U+004C "L" */
    0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc8,
    0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x10, 0x0,
    0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb5, 0x0,

    /* U+004D "M" */
    0x0, 0x16, 0xbf, 0xd8, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xfd, 0x83,
    0x0, 0x1, 0x6b, 0xff, 0xfb, 0x61, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9e, 0xff, 0xd8,
    0x30, 0x0, 0x16, 0xbf, 0xee, 0xee, 0xa4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x27, 0xde, 0xee, 0xfd,
    0x83, 0x0, 0x1, 0x6b, 0xfb, 0x76, 0xbe, 0xd8,
    0x30, 0x0, 0x0, 0x1, 0x6b, 0xec, 0x76, 0xaf,
    0xd8, 0x30, 0x0, 0x16, 0xbf, 0xb6, 0x12, 0x7c,
    0xfb, 0x61, 0x0, 0x4, 0x9e, 0xd8, 0x30, 0x4a,
    0xfd, 0x83, 0x0, 0x1, 0x6b, 0xfb, 0x61, 0x0,
    0x39, 0xde, 0x94, 0x37, 0xce, 0xa5, 0x10, 0x4,
    0xaf, 0xd8, 0x30, 0x0, 0x16, 0xbf, 0xb6, 0x10,
    0x0, 0x15, 0xae, 0xff, 0xec, 0x72, 0x0, 0x0,
    0x4a, 0xfd, 0x83, 0x0, 0x1, 0x6b, 0xfb, 0x61,
    0x0, 0x0, 0x2, 0x6b, 0xc8, 0x30, 0x0, 0x0,
    0x4, 0xaf, 0xd8, 0x30, 0x0, 0x16, 0xbf, 0xb6,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4a, 0xfd, 0x83, 0x0,

    /* U+004E "N" */
    0x0, 0x16, 0xbf, 0xe9, 0x41, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xbf, 0xd7, 0x20, 0x1, 0x6b,
    0xff, 0xfe, 0xb5, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x6b, 0xfd, 0x72, 0x0, 0x16, 0xbf, 0xdc, 0xce,
    0xfc, 0x72, 0x0, 0x0, 0x0, 0x6, 0xbf, 0xd7,
    0x20, 0x1, 0x6b, 0xfc, 0x73, 0x4a, 0xef, 0xd8,
    0x30, 0x0, 0x0, 0x6b, 0xfd, 0x72, 0x0, 0x16,
    0xbf, 0xc7, 0x20, 0x3, 0x8d, 0xfe, 0x94, 0x10,
    0x6, 0xbf, 0xd7, 0x20, 0x1, 0x6b, 0xfc, 0x72,
    0x0, 0x0, 0x27, 0xcf, 0xeb, 0x52, 0x6b, 0xfd,
    0x72, 0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0,
    0x1, 0x6b, 0xef, 0xcc, 0xdf, 0xd7, 0x20, 0x1,
    0x6b, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0, 0x14,
    0xae, 0xff, 0xfd, 0x72, 0x0, 0x16, 0xbf, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0xdf,
    0xd7, 0x20,

    /* U+004F "O" */
    0x0, 0x0, 0x0, 0x13, 0x69, 0xbc, 0xee, 0xff,
    0xfe, 0xdb, 0x97, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x59, 0xef, 0xec, 0x96, 0x53, 0x33, 0x34,
    0x68, 0xbe, 0xfe, 0xb6, 0x20, 0x0, 0x0, 0x4a,
    0xef, 0xb6, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x5a, 0xef, 0xb5, 0x10, 0x4, 0x9f, 0xe9,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x8d, 0xfb, 0x50, 0x16, 0xbf, 0xc7, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6b, 0xfc, 0x72, 0x4, 0x9f, 0xe9, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x8d,
    0xfb, 0x50, 0x0, 0x4a, 0xef, 0xb6, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x5a, 0xef, 0xb5,
    0x10, 0x0, 0x1, 0x5a, 0xef, 0xec, 0x96, 0x43,
    0x33, 0x34, 0x68, 0xbe, 0xfe, 0xb6, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x13, 0x69, 0xbd, 0xee, 0xff,
    0xfe, 0xdb, 0x97, 0x41, 0x0, 0x0, 0x0,

    /* U+0050 "P" */
    0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xdc, 0xa8, 0x52, 0x0, 0x0, 0x0, 0x1, 0x6b,
    0xfc, 0x83, 0x22, 0x22, 0x22, 0x35, 0x7a, 0xdf,
    0xd9, 0x40, 0x0, 0x0, 0x16, 0xbf, 0xc7, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9e, 0xe9, 0x40,
    0x0, 0x1, 0x6b, 0xfc, 0x72, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x39, 0xef, 0xa4, 0x0, 0x0, 0x16,
    0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x24, 0x8c,
    0xfe, 0xa5, 0x10, 0x0, 0x1, 0x6b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0xa7, 0x31, 0x0,
    0x0, 0x0, 0x16, 0xbf, 0xd8, 0x42, 0x22, 0x22,
    0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x6b, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x0, 0x13, 0x69, 0xbc, 0xee, 0xff,
    0xfe, 0xdb, 0x97, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x14, 0x9d, 0xfe, 0xc9, 0x65, 0x33,
    0x33, 0x46, 0x8b, 0xef, 0xeb, 0x62, 0x0, 0x0,
    0x0, 0x0, 0x49, 0xef, 0xb6, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x5a, 0xef, 0xb5, 0x10,
    0x0, 0x0, 0x49, 0xfe, 0x94, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0xdf, 0xb5,
    0x0, 0x0, 0x16, 0xbf, 0xc7, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6b, 0xfc,
    0x72, 0x0, 0x0, 0x4a, 0xfe, 0x94, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0xdf,
    0xb6, 0x0, 0x0, 0x0, 0x5a, 0xff, 0xb6, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x5a, 0xef,
    0xb6, 0x10, 0x0, 0x0, 0x0, 0x15, 0xae, 0xfe,
    0xb8, 0x54, 0x32, 0x22, 0x35, 0x7a, 0xdf, 0xeb,
    0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14,
    0x7a, 0xcd, 0xef, 0xff, 0xff, 0xec, 0xa8, 0x52,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x8c, 0xfe, 0xb6, 0x20,
    0x0, 0x26, 0x98, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x7b, 0xdf,
    0xff, 0xfe, 0xd9, 0x52, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+0052 "R" */
    0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xdc, 0xa8, 0x52, 0x0, 0x0, 0x0, 0x1, 0x6b,
    0xfc, 0x83, 0x22, 0x22, 0x22, 0x35, 0x7a, 0xdf,
    0xd9, 0x40, 0x0, 0x0, 0x16, 0xbf, 0xc7, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9e, 0xe9, 0x40,
    0x0, 0x1, 0x6b, 0xfc, 0x72, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x39, 0xef, 0xa5, 0x0, 0x0, 0x16,
    0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x14, 0x8b,
    0xef, 0xb5, 0x10, 0x0, 0x1, 0x6b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xb7, 0x41, 0x0,
    0x0, 0x0, 0x16, 0xbf, 0xc8, 0x32, 0x22, 0x22,
    0x26, 0xbe, 0xd9, 0x40, 0x0, 0x0, 0x0, 0x1,
    0x6b, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x1, 0x6b,
    0xfd, 0x83, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x27, 0xcf, 0xd8,
    0x30, 0x0,

    /* U+0053 "S" */
    0x0, 0x1, 0x37, 0xac, 0xde, 0xff, 0xfe, 0xdc,
    0xb8, 0x63, 0x10, 0x0, 0x1, 0x5b, 0xfe, 0xb7,
    0x43, 0x22, 0x23, 0x45, 0x79, 0x84, 0x0, 0x0,
    0x4, 0x9e, 0xe9, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x5b, 0xfe, 0xc9,
    0x63, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x25, 0x8b, 0xce, 0xff, 0xfd, 0xca,
    0x74, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x34, 0x69, 0xcf, 0xea, 0x51, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x49, 0xfe, 0x93, 0x0, 0x5, 0xad, 0xb8, 0x64,
    0x32, 0x22, 0x23, 0x48, 0xbe, 0xfb, 0x51, 0x0,
    0x0, 0x25, 0x8a, 0xcd, 0xef, 0xff, 0xfe, 0xdc,
    0xa7, 0x31, 0x0, 0x0,

    /* U+0054 "T" */
    0x0, 0x4a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x50, 0x0, 0x0, 0x12,
    0x22, 0x22, 0x23, 0x7c, 0xfc, 0x83, 0x22, 0x22,
    0x22, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x16, 0xcf, 0xc7, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6c, 0xfc,
    0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0xcf, 0xc7, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x6c, 0xfc, 0x71, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xcf,
    0xc7, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x6c, 0xfc, 0x71, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x16, 0xcf, 0xc7, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0055 "U" */
    0x0, 0x27, 0xdf, 0xb6, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x38, 0xef, 0xa4, 0x0, 0x2, 0x7d,
    0xfb, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x8e, 0xfa, 0x40, 0x0, 0x27, 0xdf, 0xb6, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0xef, 0xa4,
    0x0, 0x2, 0x7d, 0xfb, 0x61, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x8e, 0xfa, 0x40, 0x0, 0x27,
    0xdf, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x38, 0xef, 0xa4, 0x0, 0x1, 0x6c, 0xfc, 0x71,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x9f, 0xe8,
    0x30, 0x0, 0x4, 0x9e, 0xfa, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x8d, 0xfc, 0x61, 0x0, 0x0,
    0x2, 0x7c, 0xfe, 0xb8, 0x54, 0x33, 0x35, 0x7a,
    0xdf, 0xea, 0x50, 0x0, 0x0, 0x0, 0x0, 0x14,
    0x8a, 0xce, 0xff, 0xfe, 0xdd, 0xb8, 0x52, 0x0,
    0x0, 0x0,

    /* U+0056 "V" */
    0x0, 0x27, 0xcf, 0xc7, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x8d, 0xfa, 0x51, 0x0,
    0x1, 0x6b, 0xfe, 0x93, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x49, 0xee, 0x94, 0x0, 0x0, 0x0,
    0x5, 0xaf, 0xea, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xaf, 0xd8, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x39, 0xef, 0xb6, 0x10, 0x0, 0x0, 0x1, 0x6c,
    0xfc, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x7d, 0xfc, 0x72, 0x0, 0x0, 0x27, 0xdf, 0xb6,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x16,
    0xcf, 0xd8, 0x30, 0x3, 0x9e, 0xfa, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5a,
    0xfe, 0x95, 0x5a, 0xfe, 0x93, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x9e,
    0xfe, 0xef, 0xd7, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0xdf,
    0xfc, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0x27, 0xcf, 0xc7, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x9e, 0xfd, 0x83, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x28, 0xdf, 0xa4, 0x0, 0x2, 0x7d, 0xfc,
    0x61, 0x0, 0x0, 0x0, 0x0, 0x39, 0xef, 0xff,
    0xd8, 0x20, 0x0, 0x0, 0x0, 0x2, 0x7d, 0xfa,
    0x50, 0x0, 0x0, 0x28, 0xdf, 0xb6, 0x10, 0x0,
    0x0, 0x3, 0x8e, 0xd9, 0x8b, 0xfd, 0x72, 0x0,
    0x0, 0x0, 0x27, 0xcf, 0xa5, 0x0, 0x0, 0x0,
    0x3, 0x8d, 0xfb, 0x60, 0x0, 0x0, 0x38, 0xee,
    0x83, 0x5, 0xbf, 0xc7, 0x20, 0x0, 0x1, 0x7c,
    0xfb, 0x50, 0x0, 0x0, 0x0, 0x0, 0x38, 0xef,
    0xb5, 0x0, 0x3, 0x8d, 0xe9, 0x30, 0x1, 0x6b,
    0xfc, 0x71, 0x0, 0x16, 0xcf, 0xb6, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x9e, 0xfa, 0x50, 0x38,
    0xde, 0x94, 0x0, 0x0, 0x16, 0xbf, 0xc6, 0x11,
    0x6b, 0xfb, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x49, 0xef, 0xa7, 0x8d, 0xe9, 0x40, 0x0,
    0x0, 0x1, 0x6b, 0xfb, 0x77, 0xbf, 0xc6, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x9e,
    0xff, 0xfe, 0x94, 0x0, 0x0, 0x0, 0x0, 0x16,
    0xbf, 0xff, 0xfc, 0x71, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4a, 0xff, 0xea, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x6c, 0xff, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0,

    /* U+0058 "X" */
    0x0, 0x1, 0x5a, 0xef, 0xb6, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x49, 0xee, 0xb5, 0x10, 0x0, 0x0,
    0x0, 0x1, 0x5a, 0xef, 0xb6, 0x10, 0x0, 0x0,
    0x49, 0xee, 0xa5, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x4a, 0xef, 0xb6, 0x21, 0x4a, 0xee,
    0xa5, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x49, 0xef, 0xdd, 0xee, 0xa4, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x8d, 0xff, 0xe9, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x7c, 0xfd, 0xaa, 0xdf, 0xd8, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7c, 0xfd,
    0x83, 0x0, 0x27, 0xcf, 0xd8, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x7c, 0xfd, 0x83, 0x0,
    0x0, 0x0, 0x27, 0xcf, 0xd9, 0x30, 0x0, 0x0,
    0x0, 0x3, 0x8c, 0xfd, 0x83, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x27, 0xcf, 0xd9, 0x40, 0x0,

    /* U+0059 "Y" */
    0x0, 0x16, 0xcf, 0xd8, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x15, 0xae, 0xd8, 0x30, 0x0, 0x0,
    0x0, 0x38, 0xdf, 0xb6, 0x10, 0x0, 0x0, 0x0,
    0x3, 0x8d, 0xea, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4a, 0xee, 0xa5, 0x0, 0x0, 0x2, 0x7c,
    0xeb, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x6b, 0xfd, 0x83, 0x1, 0x5b, 0xed, 0x72,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x7c, 0xfc, 0xab, 0xee, 0x94, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x9e, 0xff, 0xb5, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6b, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6b,
    0xfc, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6b, 0xfc,
    0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0x16, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x50, 0x0, 0x12, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x25, 0x9d, 0xfd, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x9d,
    0xfc, 0x73, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x15, 0xae, 0xfb, 0x62, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x26, 0xbf, 0xea, 0x51,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38,
    0xcf, 0xd9, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x49, 0xdf, 0xc7, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x5b, 0xef, 0xc8,
    0x42, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x10,
    0x27, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x83,

    /* U+005B "[" */
    0x0, 0x16, 0xbf, 0xff, 0xff, 0xc7, 0x10, 0x0,
    0x1, 0x6b, 0xfb, 0x61, 0x0, 0x0, 0x0, 0x0,
    0x16, 0xbf, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x1,
    0x6b, 0xfb, 0x61, 0x0, 0x0, 0x0, 0x0, 0x16,
    0xbf, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x1, 0x6b,
    0xfb, 0x61, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf,
    0xb6, 0x10, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xfb,
    0x61, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xb6,
    0x10, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xfb, 0x61,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xb6, 0x10,
    0x0, 0x0, 0x0, 0x1, 0x6b, 0xfb, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x16, 0xbf, 0xff, 0xff, 0xc7,
    0x10, 0x0,

    /* U+005C "\\" */
    0x3, 0x57, 0x52, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x7c, 0xe9, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x27, 0xde, 0x83, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x8d, 0xd8, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0xdd,
    0x82, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x8d, 0xd8, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x38, 0xed, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x9e, 0xc7, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x49, 0xec, 0x71, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x9e, 0xc7,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49,
    0xec, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xae, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5a, 0xfb, 0x61,

    /* U+005D "]" */
    0x0, 0x17, 0xcf, 0xff, 0xff, 0xb6, 0x10, 0x0,
    0x0, 0x0, 0x1, 0x7c, 0xfb, 0x61, 0x0, 0x0,
    0x0, 0x0, 0x16, 0xcf, 0xb6, 0x10, 0x0, 0x0,
    0x0, 0x1, 0x6c, 0xfb, 0x61, 0x0, 0x0, 0x0,
    0x0, 0x16, 0xcf, 0xb6, 0x10, 0x0, 0x0, 0x0,
    0x1, 0x6c, 0xfb, 0x61, 0x0, 0x0, 0x0, 0x0,
    0x16, 0xcf, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x1,
    0x6c, 0xfb, 0x61, 0x0, 0x0, 0x0, 0x0, 0x16,
    0xcf, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x1, 0x6c,
    0xfb, 0x61, 0x0, 0x0, 0x0, 0x0, 0x16, 0xcf,
    0xb6, 0x10, 0x0, 0x0, 0x0, 0x1, 0x7c, 0xfb,
    0x61, 0x0, 0x0, 0x17, 0xcf, 0xff, 0xff, 0xb6,
    0x10, 0x0,

    /* U+005E "^" */
    0x0, 0x0, 0x0, 0x0, 0x35, 0x76, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x5a, 0xee,
    0xeb, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x6b, 0xc7, 0x47, 0xcb, 0x61, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x7c, 0xc7, 0x10, 0x17, 0xcc, 0x72,
    0x0, 0x0, 0x0, 0x3, 0x8d, 0xb6, 0x10, 0x0,
    0x16, 0xbd, 0x82, 0x0, 0x0, 0x3, 0x9d, 0xb5,
    0x0, 0x0, 0x0, 0x5, 0xbd, 0x93, 0x0,

    /* U+005F "_" */
    0x0, 0x48, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0x84, 0x0,

    /* U+0060 "`" */
    0x2, 0x47, 0x76, 0x31, 0x0, 0x0, 0x0, 0x2,
    0x58, 0xcc, 0x95, 0x10,

    /* U+0061 "a" */
    0x0, 0x25, 0x8a, 0xcd, 0xef, 0xfe, 0xec, 0xa6,
    0x30, 0x0, 0x0, 0x3, 0x7a, 0x85, 0x42, 0x22,
    0x35, 0x9d, 0xfd, 0x83, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x6b, 0xfb, 0x61, 0x0,
    0x1, 0x48, 0xbc, 0xdd, 0xee, 0xee, 0xee, 0xff,
    0xc7, 0x20, 0x4, 0x9f, 0xe9, 0x41, 0x0, 0x0,
    0x0, 0x5b, 0xfc, 0x72, 0x0, 0x4a, 0xfe, 0x94,
    0x0, 0x0, 0x3, 0x7b, 0xff, 0xc7, 0x20, 0x0,
    0x15, 0x9c, 0xde, 0xed, 0xdc, 0xb8, 0x8b, 0xfc,
    0x72, 0x0,

    /* U+0062 "b" */
    0x0, 0x39, 0xee, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x9e,
    0xe9, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x39, 0xee, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x9e, 0xea, 0x77, 0xac, 0xef, 0xff,
    0xec, 0xa7, 0x41, 0x0, 0x0, 0x0, 0x0, 0x39,
    0xef, 0xff, 0xc8, 0x53, 0x22, 0x24, 0x8b, 0xef,
    0xc7, 0x20, 0x0, 0x0, 0x3, 0x9e, 0xfc, 0x61,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xbf, 0xd8, 0x30,
    0x0, 0x0, 0x39, 0xee, 0x94, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x28, 0xdf, 0xa5, 0x0, 0x0, 0x3,
    0x9e, 0xfc, 0x61, 0x0, 0x0, 0x0, 0x0, 0x15,
    0xbf, 0xd8, 0x30, 0x0, 0x0, 0x39, 0xef, 0xff,
    0xc8, 0x53, 0x22, 0x24, 0x8b, 0xef, 0xc7, 0x20,
    0x0, 0x0, 0x3, 0x9e, 0xe9, 0x67, 0xad, 0xef,
    0xff, 0xec, 0xa7, 0x41, 0x0, 0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x24, 0x8b, 0xde, 0xff, 0xfe, 0xdb,
    0x84, 0x10, 0x0, 0x3, 0x9d, 0xfd, 0xa6, 0x32,
    0x22, 0x46, 0xac, 0xc7, 0x30, 0x5, 0xaf, 0xd8,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x7c, 0xfb, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xaf, 0xd8, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x9d, 0xfd,
    0xa6, 0x32, 0x22, 0x46, 0xad, 0xd8, 0x30, 0x0,
    0x0, 0x24, 0x8b, 0xde, 0xff, 0xfe, 0xdb, 0x84,
    0x10, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x16, 0xbf, 0xc6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc6, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x16, 0xbf, 0xc6, 0x10, 0x0, 0x0, 0x25, 0x9b,
    0xde, 0xff, 0xfd, 0xc9, 0x68, 0xcf, 0xc6, 0x10,
    0x0, 0x4a, 0xef, 0xd9, 0x63, 0x22, 0x23, 0x6a,
    0xdf, 0xff, 0xc6, 0x10, 0x5, 0xbf, 0xd8, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x49, 0xef, 0xc6, 0x10,
    0x27, 0xcf, 0xb5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x16, 0xbf, 0xc6, 0x10, 0x5, 0xbf, 0xd8, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x38, 0xef, 0xc6, 0x10,
    0x0, 0x4a, 0xef, 0xd9, 0x52, 0x10, 0x12, 0x5a,
    0xdf, 0xff, 0xc6, 0x10, 0x0, 0x0, 0x25, 0x9b,
    0xde, 0xff, 0xfe, 0xc9, 0x68, 0xbf, 0xc6, 0x10,

    /* U+0065 "e" */
    0x0, 0x0, 0x25, 0x9b, 0xde, 0xff, 0xed, 0xc9,
    0x52, 0x0, 0x0, 0x0, 0x0, 0x49, 0xee, 0xc8,
    0x42, 0x11, 0x24, 0x7b, 0xee, 0x94, 0x0, 0x0,
    0x5, 0xaf, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x1,
    0x6c, 0xfb, 0x50, 0x0, 0x27, 0xcf, 0xff, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xfc, 0x72, 0x0,
    0x5, 0xaf, 0xd8, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xef, 0xd9,
    0x64, 0x22, 0x23, 0x47, 0xaa, 0x62, 0x0, 0x0,
    0x0, 0x0, 0x25, 0x8b, 0xde, 0xff, 0xfe, 0xdc,
    0x95, 0x20, 0x0, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x0, 0x1, 0x59, 0xce, 0xff, 0xeb,
    0x62, 0x0, 0x0, 0x0, 0x49, 0xee, 0xa5, 0x11,
    0x22, 0x10, 0x0, 0x0, 0x0, 0x6b, 0xfb, 0x61,
    0x0, 0x0, 0x0, 0x0, 0x27, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xe9, 0x30, 0x0, 0x0, 0x1, 0x6b,
    0xfb, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x6b, 0xfb, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x6b, 0xfb, 0x61, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x6b, 0xfb, 0x61, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x6b, 0xfb, 0x61, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x6b, 0xfb, 0x61, 0x0,
    0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x25, 0x9b, 0xde, 0xff, 0xfe, 0xc9,
    0x67, 0xae, 0xd8, 0x20, 0x0, 0x49, 0xef, 0xda,
    0x64, 0x22, 0x23, 0x59, 0xcf, 0xff, 0xd8, 0x20,
    0x5, 0xaf, 0xd8, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x27, 0xdf, 0xd8, 0x20, 0x27, 0xcf, 0xb5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xaf, 0xd8, 0x20,
    0x5, 0xaf, 0xd8, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x28, 0xdf, 0xd8, 0x20, 0x0, 0x49, 0xef, 0xda,
    0x63, 0x22, 0x23, 0x6a, 0xdf, 0xff, 0xd8, 0x20,
    0x0, 0x0, 0x25, 0x9b, 0xde, 0xff, 0xfe, 0xc9,
    0x57, 0xaf, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x27, 0xcf, 0xb5, 0x0,
    0x0, 0x38, 0xcc, 0x86, 0x43, 0x22, 0x23, 0x58,
    0xbe, 0xea, 0x50, 0x0, 0x0, 0x13, 0x69, 0xbd,
    0xef, 0xff, 0xfe, 0xdc, 0xa6, 0x31, 0x0, 0x0,

    /* U+0068 "h" */
    0x0, 0x39, 0xee, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xee, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x39, 0xee, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xee, 0xa7,
    0x6a, 0xde, 0xff, 0xee, 0xc9, 0x52, 0x0, 0x0,
    0x0, 0x39, 0xef, 0xfe, 0xb7, 0x43, 0x22, 0x46,
    0xae, 0xfc, 0x72, 0x0, 0x0, 0x39, 0xef, 0xb6,
    0x10, 0x0, 0x0, 0x0, 0x27, 0xcf, 0xb6, 0x0,
    0x0, 0x39, 0xee, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xbf, 0xc7, 0x10, 0x0, 0x39, 0xee, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xbf, 0xc7, 0x20,
    0x0, 0x39, 0xee, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xbf, 0xc7, 0x20, 0x0, 0x39, 0xee, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xbf, 0xc7, 0x20,

    /* U+0069 "i" */
    0x0, 0x49, 0xdd, 0x94, 0x0, 0x2, 0x58, 0x85,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x9e,
    0xe9, 0x40, 0x0, 0x39, 0xee, 0x94, 0x0, 0x3,
    0x9e, 0xe9, 0x40, 0x0, 0x39, 0xee, 0x94, 0x0,
    0x3, 0x9e, 0xe9, 0x40, 0x0, 0x39, 0xee, 0x94,
    0x0, 0x3, 0x9e, 0xe9, 0x40,

    /* U+006A "j" */
    0x0, 0x0, 0x0, 0x0, 0x38, 0xde, 0xa5, 0x10,
    0x0, 0x0, 0x0, 0x1, 0x47, 0x85, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x8d, 0xfa, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x28, 0xdf, 0xa4, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x8d, 0xfa, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x28, 0xdf, 0xa4, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x8d, 0xfa, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x28, 0xdf, 0xa4, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x8d, 0xfa, 0x40, 0x0, 0x0, 0x0, 0x0, 0x28,
    0xdf, 0x94, 0x0, 0x0, 0x22, 0x21, 0x27, 0xbf,
    0xd7, 0x20, 0x0, 0x38, 0xdf, 0xff, 0xec, 0x84,
    0x10, 0x0,

    /* U+006B "k" */
    0x0, 0x39, 0xee, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xee, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x39, 0xee, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xee, 0x94,
    0x0, 0x0, 0x0, 0x27, 0xbe, 0xda, 0x51, 0x0,
    0x0, 0x39, 0xee, 0x94, 0x0, 0x2, 0x7b, 0xee,
    0xb6, 0x20, 0x0, 0x0, 0x0, 0x39, 0xee, 0x94,
    0x27, 0xbe, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x39, 0xef, 0xee, 0xef, 0xff, 0xfd, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xef, 0xfc,
    0x73, 0x13, 0x8d, 0xfd, 0x94, 0x0, 0x0, 0x0,
    0x0, 0x39, 0xee, 0x94, 0x0, 0x0, 0x2, 0x7c,
    0xfe, 0xa4, 0x10, 0x0, 0x0, 0x39, 0xee, 0x94,
    0x0, 0x0, 0x0, 0x2, 0x6b, 0xfe, 0xa5, 0x10,

    /* U+006C "l" */
    0x0, 0x39, 0xee, 0x94, 0x0, 0x3, 0x9e, 0xe9,
    0x40, 0x0, 0x39, 0xee, 0x94, 0x0, 0x3, 0x9e,
    0xe9, 0x40, 0x0, 0x39, 0xee, 0x94, 0x0, 0x3,
    0x9e, 0xe9, 0x40, 0x0, 0x39, 0xee, 0x94, 0x0,
    0x3, 0x9e, 0xe9, 0x40, 0x0, 0x39, 0xee, 0x94,
    0x0, 0x3, 0x9e, 0xe9, 0x40,

    /* U+006D "m" */
    0x0, 0x39, 0xee, 0x97, 0x7b, 0xde, 0xff, 0xfd,
    0xb9, 0x42, 0x25, 0x9b, 0xde, 0xff, 0xed, 0xb8,
    0x41, 0x0, 0x0, 0x39, 0xef, 0xfd, 0xa6, 0x32,
    0x12, 0x47, 0xcf, 0xff, 0xfe, 0xb6, 0x32, 0x12,
    0x37, 0xbe, 0xfa, 0x51, 0x0, 0x39, 0xef, 0xb6,
    0x10, 0x0, 0x0, 0x0, 0x49, 0xff, 0xc6, 0x10,
    0x0, 0x0, 0x0, 0x49, 0xee, 0x94, 0x0, 0x39,
    0xee, 0x94, 0x0, 0x0, 0x0, 0x0, 0x38, 0xef,
    0xa4, 0x0, 0x0, 0x0, 0x0, 0x28, 0xdf, 0xa5,
    0x0, 0x39, 0xee, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x38, 0xef, 0xa4, 0x0, 0x0, 0x0, 0x0, 0x28,
    0xdf, 0xa5, 0x0, 0x39, 0xee, 0x94, 0x0, 0x0,
    0x0, 0x0, 0x38, 0xef, 0xa4, 0x0, 0x0, 0x0,
    0x0, 0x28, 0xdf, 0xa5, 0x0, 0x39, 0xee, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x38, 0xef, 0xa4, 0x0,
    0x0, 0x0, 0x0, 0x28, 0xdf, 0xa5,

    /* U+006E "n" */
    0x0, 0x39, 0xee, 0x96, 0x7a, 0xde, 0xff, 0xee,
    0xc9, 0x52, 0x0, 0x0, 0x0, 0x39, 0xef, 0xfe,
    0xa6, 0x42, 0x11, 0x35, 0xad, 0xfc, 0x72, 0x0,
    0x0, 0x39, 0xef, 0xb6, 0x10, 0x0, 0x0, 0x0,
    0x17, 0xcf, 0xb6, 0x0, 0x0, 0x39, 0xee, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xbf, 0xc7, 0x10,
    0x0, 0x39, 0xee, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xbf, 0xc7, 0x20, 0x0, 0x39, 0xee, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xbf, 0xc7, 0x20,
    0x0, 0x39, 0xee, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xbf, 0xc7, 0x20,

    /* U+006F "o" */
    0x0, 0x0, 0x25, 0x8b, 0xde, 0xff, 0xfe, 0xdb,
    0x84, 0x10, 0x0, 0x0, 0x0, 0x49, 0xef, 0xda,
    0x63, 0x22, 0x23, 0x6a, 0xef, 0xd8, 0x30, 0x0,
    0x5, 0xaf, 0xd8, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x49, 0xef, 0xa4, 0x0, 0x27, 0xcf, 0xb5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc6, 0x10,
    0x5, 0xaf, 0xd8, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x49, 0xef, 0xa4, 0x0, 0x0, 0x49, 0xdf, 0xda,
    0x63, 0x21, 0x23, 0x6a, 0xef, 0xd8, 0x30, 0x0,
    0x0, 0x0, 0x25, 0x8b, 0xde, 0xff, 0xfe, 0xdb,
    0x84, 0x10, 0x0, 0x0,

    /* U+0070 "p" */
    0x0, 0x39, 0xee, 0x97, 0x7b, 0xde, 0xff, 0xfe,
    0xca, 0x74, 0x10, 0x0, 0x0, 0x0, 0x3, 0x9e,
    0xff, 0xfc, 0x84, 0x21, 0x11, 0x36, 0xae, 0xfc,
    0x72, 0x0, 0x0, 0x0, 0x39, 0xef, 0xc6, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x5a, 0xfd, 0x83, 0x0,
    0x0, 0x3, 0x9e, 0xe9, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x8d, 0xfa, 0x50, 0x0, 0x0, 0x39,
    0xef, 0xc7, 0x10, 0x0, 0x0, 0x0, 0x1, 0x6b,
    0xfd, 0x83, 0x0, 0x0, 0x3, 0x9e, 0xff, 0xfc,
    0x95, 0x32, 0x22, 0x48, 0xcf, 0xfc, 0x72, 0x0,
    0x0, 0x0, 0x39, 0xee, 0x96, 0x6a, 0xce, 0xff,
    0xfe, 0xca, 0x74, 0x10, 0x0, 0x0, 0x0, 0x3,
    0x9e, 0xe9, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xee, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x9e, 0xe9, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x25, 0x9b, 0xde, 0xff, 0xfd, 0xc9,
    0x57, 0xbf, 0xc6, 0x10, 0x0, 0x4a, 0xef, 0xda,
    0x63, 0x22, 0x23, 0x6b, 0xef, 0xff, 0xc6, 0x10,
    0x5, 0xbf, 0xd8, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x49, 0xef, 0xc6, 0x10, 0x27, 0xcf, 0xb5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc6, 0x10,
    0x5, 0xbf, 0xd8, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x49, 0xef, 0xc6, 0x10, 0x0, 0x4a, 0xef, 0xda,
    0x63, 0x21, 0x23, 0x6a, 0xef, 0xff, 0xc6, 0x10,
    0x0, 0x0, 0x25, 0x9b, 0xde, 0xff, 0xfd, 0xb8,
    0x57, 0xbf, 0xc6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc6, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x16, 0xbf, 0xc6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc6, 0x10,

    /* U+0072 "r" */
    0x0, 0x39, 0xee, 0x96, 0x7a, 0xde, 0xc8, 0x20,
    0x3, 0x9e, 0xff, 0xec, 0x86, 0x43, 0x10, 0x0,
    0x39, 0xef, 0xc7, 0x10, 0x0, 0x0, 0x0, 0x3,
    0x9e, 0xf9, 0x40, 0x0, 0x0, 0x0, 0x0, 0x39,
    0xee, 0x94, 0x0, 0x0, 0x0, 0x0, 0x3, 0x9e,
    0xe9, 0x40, 0x0, 0x0, 0x0, 0x0, 0x39, 0xee,
    0x94, 0x0, 0x0, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x0, 0x2, 0x59, 0xbd, 0xef, 0xff, 0xed,
    0xca, 0x62, 0x0, 0x0, 0x16, 0xbf, 0xd9, 0x52,
    0x11, 0x22, 0x46, 0x64, 0x10, 0x0, 0x1, 0x7c,
    0xfd, 0x95, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x26, 0x9c, 0xef, 0xff, 0xed, 0xc9,
    0x63, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x12, 0x48, 0xcf, 0xe9, 0x30, 0x0, 0x15, 0x89,
    0x64, 0x32, 0x11, 0x23, 0x7b, 0xfd, 0x83, 0x0,
    0x1, 0x48, 0xac, 0xde, 0xff, 0xfe, 0xec, 0xa7,
    0x30, 0x0,

    /* U+0074 "t" */
    0x0, 0x0, 0x0, 0x35, 0x85, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x6b, 0xfb, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x27, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xe9, 0x30, 0x0, 0x0, 0x1, 0x6b, 0xfb,
    0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b,
    0xfb, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x6b, 0xfb, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6b, 0xfb, 0x61, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4a, 0xfe, 0xa5, 0x21, 0x22, 0x10,
    0x0, 0x0, 0x0, 0x2, 0x6a, 0xde, 0xff, 0xeb,
    0x62,

    /* U+0075 "u" */
    0x0, 0x4a, 0xfd, 0x83, 0x0, 0x0, 0x0, 0x0,
    0x27, 0xcf, 0xb5, 0x0, 0x0, 0x4a, 0xfd, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x27, 0xcf, 0xb5, 0x0,
    0x0, 0x4a, 0xfd, 0x83, 0x0, 0x0, 0x0, 0x0,
    0x27, 0xcf, 0xb5, 0x0, 0x0, 0x4a, 0xfd, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x27, 0xcf, 0xb5, 0x0,
    0x0, 0x39, 0xee, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x49, 0xef, 0xb5, 0x0, 0x0, 0x5, 0xae, 0xeb,
    0x73, 0x21, 0x12, 0x59, 0xcf, 0xff, 0xb5, 0x0,
    0x0, 0x0, 0x14, 0x7b, 0xde, 0xff, 0xfe, 0xc8,
    0x68, 0xcf, 0xb5, 0x0,

    /* U+0076 "v" */
    0x0, 0x28, 0xdf, 0xb5, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x49, 0xed, 0x83, 0x0, 0x0, 0x0, 0x16,
    0xcf, 0xc6, 0x10, 0x0, 0x0, 0x0, 0x5a, 0xfc,
    0x72, 0x0, 0x0, 0x0, 0x0, 0x15, 0xbf, 0xd7,
    0x20, 0x0, 0x1, 0x6b, 0xfb, 0x61, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xae, 0xd8, 0x30, 0x2,
    0x7c, 0xfa, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x9e, 0xe9, 0x43, 0x8d, 0xe9, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x8d, 0xfd, 0xde, 0xd8, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6c, 0xff,
    0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0077 "w" */
    0x0, 0x16, 0xcf, 0xa5, 0x0, 0x0, 0x0, 0x0,
    0x39, 0xef, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x27,
    0xdd, 0x83, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xa5,
    0x0, 0x0, 0x0, 0x49, 0xef, 0xff, 0xb6, 0x10,
    0x0, 0x0, 0x27, 0xde, 0x83, 0x0, 0x0, 0x0,
    0x0, 0x16, 0xcf, 0xa5, 0x0, 0x0, 0x49, 0xec,
    0x77, 0xae, 0xb6, 0x10, 0x0, 0x27, 0xdd, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xa5,
    0x0, 0x49, 0xec, 0x71, 0x4, 0xae, 0xc6, 0x10,
    0x28, 0xdd, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x16, 0xbe, 0xa5, 0x59, 0xeb, 0x61, 0x0,
    0x4, 0xae, 0xb6, 0x48, 0xdd, 0x83, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xee,
    0xeb, 0x61, 0x0, 0x0, 0x4, 0x9e, 0xee, 0xed,
    0x82, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x16, 0xbf, 0xfb, 0x61, 0x0, 0x0, 0x0,
    0x4, 0x9e, 0xfd, 0x82, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+0078 "x" */
    0x0, 0x1, 0x5b, 0xed, 0x94, 0x0, 0x0, 0x1,
    0x49, 0xee, 0xa4, 0x10, 0x0, 0x0, 0x1, 0x5a,
    0xee, 0x94, 0x11, 0x4a, 0xee, 0x94, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x4a, 0xee, 0xdd, 0xed,
    0x93, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x8d, 0xff, 0xc6, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x6b, 0xed, 0xab, 0xde,
    0xa5, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7c,
    0xec, 0x72, 0x0, 0x38, 0xde, 0xb6, 0x10, 0x0,
    0x0, 0x2, 0x7c, 0xfc, 0x72, 0x0, 0x0, 0x0,
    0x38, 0xdf, 0xc6, 0x20,

    /* U+0079 "y" */
    0x0, 0x28, 0xdf, 0xb5, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x49, 0xed, 0x83, 0x0, 0x0, 0x0, 0x27,
    0xcf, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x4a, 0xec,
    0x72, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xc7,
    0x20, 0x0, 0x0, 0x5a, 0xfc, 0x61, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xaf, 0xd7, 0x20, 0x1,
    0x6b, 0xfb, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xae, 0xd8, 0x31, 0x6c, 0xea, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x9e, 0xeb, 0xbd, 0xe9, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x8d, 0xff,
    0xe8, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xaf, 0xd7, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0x53, 0x22,
    0x48, 0xcf, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x14, 0x9c, 0xef, 0xfe, 0xc9, 0x52,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x27, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x7c, 0xfd, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x38, 0xcf, 0xc7, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x9d, 0xeb, 0x62, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x49, 0xee, 0xb5, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0xae, 0xea,
    0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x28,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd7,
    0x20, 0x0,

    /* U+007B "{" */
    0x0, 0x0, 0x2, 0x7b, 0xdf, 0xfa, 0x50, 0x0,
    0x0, 0x4, 0x9f, 0xea, 0x51, 0x0, 0x0, 0x0,
    0x0, 0x5a, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xbf, 0xc7, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x5b, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0, 0x16,
    0xbf, 0xc7, 0x10, 0x0, 0x0, 0x4, 0x9f, 0xff,
    0xc6, 0x20, 0x0, 0x0, 0x0, 0x1, 0x38, 0xcf,
    0xc6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x5b, 0xfc,
    0x72, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbf, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xfc, 0x72,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9f, 0xea, 0x51,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x7b, 0xdf, 0xfa,
    0x50, 0x0,

    /* U+007C "|" */
    0x0, 0x16, 0xbf, 0xa5, 0x0, 0x1, 0x6b, 0xfa,
    0x50, 0x0, 0x16, 0xbf, 0xa5, 0x0, 0x1, 0x6b,
    0xfa, 0x50, 0x0, 0x16, 0xbf, 0xa5, 0x0, 0x1,
    0x6b, 0xfa, 0x50, 0x0, 0x16, 0xbf, 0xa5, 0x0,
    0x1, 0x6b, 0xfa, 0x50, 0x0, 0x16, 0xbf, 0xa5,
    0x0, 0x1, 0x6b, 0xfa, 0x50, 0x0, 0x16, 0xbf,
    0xa5, 0x0, 0x1, 0x6b, 0xfa, 0x50, 0x0, 0x16,
    0xbf, 0xa5, 0x0,

    /* U+007D "}" */
    0x0, 0x17, 0xcf, 0xed, 0xa5, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x27, 0xcf, 0xd7, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x49, 0xee, 0x93, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x9e, 0xe9, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x49, 0xee, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x8e, 0xfa, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x8d, 0xff, 0xc7, 0x20, 0x0, 0x0, 0x3,
    0x8d, 0xfb, 0x62, 0x0, 0x0, 0x0, 0x0, 0x49,
    0xee, 0x94, 0x0, 0x0, 0x0, 0x0, 0x4, 0x9e,
    0xe9, 0x40, 0x0, 0x0, 0x0, 0x0, 0x49, 0xee,
    0x93, 0x0, 0x0, 0x0, 0x0, 0x27, 0xcf, 0xd8,
    0x20, 0x0, 0x0, 0x17, 0xcf, 0xed, 0xa5, 0x20,
    0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x26, 0xbd, 0xee, 0xdb, 0x84, 0x20, 0x5,
    0xac, 0x83, 0x0, 0x38, 0xc9, 0x51, 0x12, 0x58,
    0xbd, 0xdd, 0xca, 0x62, 0x0,

    /* U+00B0 "°" */
    0x0, 0x3, 0x6a, 0xbb, 0xba, 0x73, 0x0, 0x0,
    0x38, 0xb8, 0x30, 0x0, 0x38, 0xb9, 0x40, 0x16,
    0xa9, 0x40, 0x0, 0x0, 0x39, 0xb7, 0x20, 0x38,
    0xb8, 0x30, 0x0, 0x38, 0xb9, 0x40, 0x0, 0x3,
    0x6a, 0xbb, 0xba, 0x73, 0x0, 0x0,

    /* U+2022 "•" */
    0x0, 0x2, 0x45, 0x42, 0x0, 0x0, 0x2, 0x7c,
    0xff, 0xfe, 0x83, 0x0, 0x0, 0x49, 0xdf, 0xea,
    0x51, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x12, 0x32, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x13, 0x46, 0x79, 0xbc, 0xef, 0xff, 0xff,
    0xa4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x13, 0x56, 0x89, 0xbc, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x9e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xed, 0xca, 0x87, 0x54, 0x23, 0x8d, 0xff, 0xa5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xaf,
    0xff, 0xda, 0x87, 0x53, 0x21, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x8d, 0xff, 0xa5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xaf, 0xfd, 0x82, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x8d,
    0xff, 0xa5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xaf, 0xfd, 0x82, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x8d, 0xff, 0xa5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xaf, 0xfd,
    0x82, 0x0, 0x0, 0x0, 0x0, 0x24, 0x79, 0xbb,
    0xbc, 0xdf, 0xff, 0xa5, 0x0, 0x0, 0x0, 0x0,
    0x12, 0x33, 0x37, 0xbf, 0xfd, 0x82, 0x0, 0x0,
    0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa5, 0x0, 0x0, 0x15, 0xad, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x82, 0x0, 0x0, 0x0, 0x2, 0x6b,
    0xef, 0xff, 0xff, 0xff, 0xd9, 0x41, 0x0, 0x0,
    0x39, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x61,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22, 0x32,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x25, 0x89,
    0xab, 0xaa, 0x87, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F008 "" */
    0x0, 0x39, 0xb9, 0x54, 0x59, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x95, 0x45, 0x9b, 0x93, 0x0, 0x0, 0x5a, 0xeb,
    0x88, 0x9b, 0xef, 0xb7, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x23, 0x7c, 0xfe, 0xb8, 0x88, 0xbe,
    0xa5, 0x0, 0x0, 0x5a, 0xc7, 0x10, 0x17, 0xcf,
    0xb5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x6b, 0xfc, 0x61, 0x1, 0x7c, 0xa5, 0x0, 0x0,
    0x5a, 0xfd, 0xcc, 0xce, 0xff, 0xb6, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x12, 0x7c, 0xff, 0xdc,
    0xcc, 0xef, 0xa5, 0x0, 0x0, 0x5a, 0xc6, 0x10,
    0x17, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x61, 0x1, 0x6c, 0xa5,
    0x0, 0x0, 0x5a, 0xfd, 0xcc, 0xce, 0xff, 0xb6,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x12, 0x7c,
    0xff, 0xdc, 0xcc, 0xef, 0xa5, 0x0, 0x0, 0x5a,
    0xc7, 0x10, 0x17, 0xcf, 0xb5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x6b, 0xfc, 0x61, 0x1,
    0x7c, 0xa5, 0x0, 0x0, 0x5a, 0xeb, 0x88, 0x9b,
    0xef, 0xb7, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x23, 0x7c, 0xfe, 0xb8, 0x88, 0xbe, 0xa5, 0x0,
    0x0, 0x49, 0xb9, 0x54, 0x59, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x95, 0x45, 0x9b, 0x93, 0x0,

    /* U+F00B "" */
    0x0, 0x38, 0xdf, 0xff, 0xff, 0xfe, 0xb6, 0x25,
    0x9e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x83, 0x0, 0x0, 0x5a, 0xff,
    0xff, 0xff, 0xff, 0xd8, 0x46, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa5, 0x0, 0x0, 0x49, 0xef, 0xff, 0xff, 0xff,
    0xc6, 0x25, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x94, 0x0, 0x0,
    0x0, 0x12, 0x33, 0x33, 0x22, 0x10, 0x0, 0x1,
    0x23, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x21, 0x0, 0x0, 0x0, 0x4a, 0xff, 0xff,
    0xff, 0xff, 0xc7, 0x26, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa4,
    0x0, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xd8,
    0x46, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa5, 0x0, 0x0, 0x4a,
    0xff, 0xff, 0xff, 0xff, 0xc7, 0x26, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa4, 0x0, 0x0, 0x0, 0x12, 0x33, 0x33,
    0x22, 0x10, 0x0, 0x1, 0x23, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x21, 0x0, 0x0,
    0x0, 0x49, 0xef, 0xff, 0xff, 0xff, 0xc6, 0x25,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x94, 0x0, 0x0, 0x5a, 0xff,
    0xff, 0xff, 0xff, 0xd8, 0x46, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa5, 0x0, 0x0, 0x38, 0xdf, 0xff, 0xff, 0xfe,
    0xb6, 0x25, 0xae, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x83, 0x0,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x7c, 0xdc, 0x94, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x38, 0xcf, 0xff, 0xff, 0xfe,
    0x93, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x8c,
    0xff, 0xff, 0xff, 0xfd, 0x84, 0x10, 0x0, 0x0,
    0x1, 0x49, 0xcd, 0xc7, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x38, 0xcf, 0xff, 0xff, 0xff, 0xd8,
    0x41, 0x0, 0x0, 0x0, 0x0, 0x39, 0xef, 0xff,
    0xff, 0xfc, 0x83, 0x0, 0x0, 0x3, 0x8c, 0xff,
    0xff, 0xff, 0xfd, 0x84, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x48, 0xdf, 0xff, 0xff, 0xff,
    0xc8, 0x68, 0xcf, 0xff, 0xff, 0xff, 0xd8, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x14, 0x8c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x84, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x48, 0xcf, 0xff, 0xff, 0xff, 0xc8, 0x41, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x8c,
    0xdc, 0x83, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x0, 0x0, 0x13, 0x44, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x24, 0x42, 0x10, 0x0, 0x0,
    0x27, 0xdf, 0xff, 0xfc, 0x73, 0x0, 0x0, 0x0,
    0x14, 0x8d, 0xff, 0xff, 0xb6, 0x10, 0x0, 0x14,
    0x9d, 0xff, 0xff, 0xff, 0xc7, 0x32, 0x48, 0xdf,
    0xff, 0xff, 0xfd, 0x83, 0x0, 0x0, 0x0, 0x1,
    0x5a, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd9, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x16, 0xbf, 0xff, 0xff, 0xff, 0xfe, 0xa5, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x8d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x73, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x48, 0xdf, 0xff, 0xff,
    0xfd, 0x98, 0xad, 0xff, 0xff, 0xff, 0xc7, 0x30,
    0x0, 0x0, 0x39, 0xef, 0xff, 0xff, 0xd9, 0x41,
    0x0, 0x1, 0x5a, 0xdf, 0xff, 0xff, 0xd7, 0x20,
    0x0, 0x2, 0x59, 0xbb, 0x84, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x15, 0x9b, 0xb9, 0x51, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x47, 0x99, 0x74, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x36, 0x52, 0x0, 0x4, 0x9f, 0xff,
    0xf9, 0x40, 0x0, 0x25, 0x64, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0x9d, 0xff,
    0xfc, 0x71, 0x4, 0x9f, 0xff, 0xf9, 0x40, 0x17,
    0xcf, 0xff, 0xea, 0x51, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x15, 0xaf, 0xff, 0xfd, 0x94, 0x10, 0x4,
    0x9f, 0xff, 0xf9, 0x40, 0x1, 0x59, 0xdf, 0xff,
    0xfb, 0x51, 0x0, 0x0, 0x0, 0x2, 0x7d, 0xff,
    0xfd, 0x83, 0x0, 0x0, 0x4, 0x9f, 0xff, 0xf9,
    0x40, 0x0, 0x0, 0x38, 0xdf, 0xff, 0xc7, 0x20,
    0x0, 0x0, 0x16, 0xbf, 0xff, 0xd8, 0x20, 0x0,
    0x0, 0x4, 0x9f, 0xff, 0xf9, 0x40, 0x0, 0x0,
    0x2, 0x8d, 0xff, 0xfb, 0x60, 0x0, 0x0, 0x17,
    0xcf, 0xff, 0xc6, 0x10, 0x0, 0x0, 0x3, 0x9e,
    0xff, 0xe9, 0x30, 0x0, 0x0, 0x1, 0x6c, 0xff,
    0xfc, 0x71, 0x0, 0x0, 0x5, 0xaf, 0xff, 0xe9,
    0x40, 0x0, 0x0, 0x0, 0x1, 0x11, 0x10, 0x0,
    0x0, 0x0, 0x4, 0x9e, 0xff, 0xfa, 0x50, 0x0,
    0x0, 0x1, 0x5a, 0xff, 0xff, 0xb6, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b,
    0xff, 0xff, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x3,
    0x8d, 0xff, 0xff, 0xda, 0x63, 0x10, 0x0, 0x0,
    0x0, 0x1, 0x36, 0xad, 0xff, 0xff, 0xd8, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x5a, 0xdf,
    0xff, 0xff, 0xfe, 0xdc, 0xcc, 0xcd, 0xef, 0xff,
    0xff, 0xfd, 0xa5, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x68, 0xad, 0xef,
    0xff, 0xff, 0xff, 0xfe, 0xdb, 0x96, 0x31, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x23,
    0x44, 0x44, 0x32, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x27, 0xdf, 0xff, 0xff, 0xfd, 0x72, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x35, 0x64,
    0x11, 0x36, 0xad, 0xff, 0xff, 0xff, 0xff, 0xda,
    0x63, 0x11, 0x46, 0x53, 0x0, 0x0, 0x0, 0x49,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x94, 0x0,
    0x16, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xba, 0xab, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x61, 0x1, 0x37, 0xbe, 0xff, 0xff, 0xff,
    0xc7, 0x20, 0x0, 0x0, 0x2, 0x7c, 0xff, 0xff,
    0xff, 0xeb, 0x73, 0x10, 0x0, 0x2, 0x7c, 0xff,
    0xff, 0xfd, 0x82, 0x0, 0x0, 0x0, 0x0, 0x28,
    0xdf, 0xff, 0xff, 0xd7, 0x20, 0x0, 0x1, 0x37,
    0xbe, 0xff, 0xff, 0xff, 0xc7, 0x20, 0x0, 0x0,
    0x2, 0x7c, 0xff, 0xff, 0xff, 0xeb, 0x73, 0x10,
    0x16, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xba, 0xab, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x61, 0x0, 0x49, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x94, 0x0, 0x0, 0x0, 0x35, 0x64,
    0x11, 0x25, 0x9c, 0xff, 0xff, 0xff, 0xff, 0xc9,
    0x52, 0x11, 0x36, 0x53, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x27, 0xdf, 0xff, 0xff,
    0xfd, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x23,
    0x44, 0x44, 0x32, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x25, 0x77, 0x63, 0x10, 0x0, 0x3,
    0x68, 0x88, 0x63, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x59,
    0xdf, 0xff, 0xff, 0xfe, 0xb6, 0x32, 0x7c, 0xff,
    0xfc, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x13, 0x7c, 0xef, 0xfe, 0xc8,
    0x54, 0x6a, 0xdf, 0xff, 0xee, 0xef, 0xff, 0xc7,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x26, 0xad, 0xff, 0xfd, 0xa5, 0x34, 0x8b, 0xc9,
    0x53, 0x47, 0xbe, 0xff, 0xff, 0xfc, 0x71, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x59, 0xcf, 0xff,
    0xeb, 0x74, 0x36, 0xad, 0xff, 0xff, 0xff, 0xec,
    0x84, 0x35, 0x9d, 0xff, 0xfe, 0xb6, 0x30, 0x0,
    0x0, 0x2, 0x6b, 0xef, 0xff, 0xc9, 0x53, 0x59,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xb7, 0x33, 0x6b, 0xef, 0xff, 0xd8, 0x40, 0x0,
    0x3, 0x7a, 0x96, 0x33, 0x6b, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd9, 0x42, 0x48, 0xa9, 0x51, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x6c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x16, 0xcf, 0xff, 0xff, 0xff, 0xfa, 0x50, 0x0,
    0x2, 0x7d, 0xff, 0xff, 0xff, 0xfe, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6c,
    0xff, 0xff, 0xff, 0xff, 0xa4, 0x0, 0x0, 0x27,
    0xcf, 0xff, 0xff, 0xff, 0xe9, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xae, 0xff,
    0xff, 0xff, 0xd8, 0x30, 0x0, 0x1, 0x6b, 0xff,
    0xff, 0xff, 0xfd, 0x83, 0x0, 0x0, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x24, 0x67, 0x77, 0x76, 0x42, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x7d, 0xff, 0xff,
    0xff, 0xd7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x8d, 0xff, 0xff, 0xff, 0xd8, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x8d,
    0xff, 0xff, 0xff, 0xd8, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x8d, 0xff, 0xff, 0xff,
    0xd8, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x49, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x94, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x14, 0x9d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd9, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x23, 0x33, 0x33, 0x33, 0x33, 0x22,
    0x59, 0xdf, 0xff, 0xfd, 0x95, 0x22, 0x33, 0x33,
    0x33, 0x33, 0x32, 0x10, 0x0, 0x0, 0x4a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xa5, 0x34, 0x77,
    0x43, 0x5a, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa4, 0x0, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xec, 0xaa, 0xce, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x0, 0x0,
    0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe9, 0x56, 0xbc, 0x75,
    0x8d, 0xff, 0xa5, 0x0, 0x0, 0x25, 0x9a, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8, 0x52,
    0x0,

    /* U+F01C "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x49, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xb7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x15, 0xae, 0xff, 0xda, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x9b, 0xef,
    0xfc, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x5a, 0xef, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0xae, 0xff,
    0xd7, 0x30, 0x0, 0x0, 0x0, 0x15, 0xae, 0xff,
    0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x4a, 0xef, 0xfc,
    0x82, 0x0, 0x0, 0x38, 0xef, 0xff, 0xda, 0x88,
    0x88, 0x88, 0x63, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x25, 0x78, 0x88, 0x88, 0x9b, 0xef, 0xff, 0xb6,
    0x10, 0x5, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd8, 0x30, 0x0, 0x0, 0x1, 0x5b, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x82, 0x0,
    0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd8, 0x20, 0x5, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x72, 0x0, 0x26, 0xbe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x84, 0x0,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x11, 0x11, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x58, 0x99, 0x62, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x13, 0x69, 0xbd, 0xef, 0xff, 0xff,
    0xff, 0xfd, 0xb9, 0x74, 0x20, 0x4, 0x9f, 0xff,
    0xa5, 0x0, 0x0, 0x0, 0x0, 0x3, 0x7b, 0xef,
    0xff, 0xff, 0xdc, 0xaa, 0x99, 0x9a, 0xcd, 0xff,
    0xff, 0xfd, 0xaa, 0xbf, 0xff, 0xa5, 0x0, 0x0,
    0x0, 0x4, 0x9e, 0xff, 0xfe, 0xa6, 0x31, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x59, 0xcf, 0xff,
    0xff, 0xff, 0xa5, 0x0, 0x0, 0x2, 0x7c, 0xff,
    0xfc, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38,
    0xef, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xa5,
    0x0, 0x0, 0x3, 0x6a, 0xa9, 0x73, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x25, 0x9a, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xa9, 0x63, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x36, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xa9, 0x52, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x37, 0xaa, 0xa6, 0x30, 0x0,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0xfe, 0xee, 0xff,
    0xfe, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27,
    0xcf, 0xff, 0xc7, 0x20, 0x0, 0x0, 0x5a, 0xff,
    0xff, 0xff, 0xfc, 0x95, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x13, 0x7b, 0xef, 0xff, 0xe9, 0x40,
    0x0, 0x0, 0x0, 0x5a, 0xff, 0xfb, 0xaa, 0xdf,
    0xff, 0xff, 0xdb, 0xa9, 0x99, 0xaa, 0xcd, 0xff,
    0xff, 0xfe, 0xb7, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x5a, 0xff, 0xfa, 0x40, 0x2, 0x47, 0x9c, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xdb, 0x96, 0x31, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x26, 0x99, 0x85,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x26, 0xaa, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x6b, 0xef, 0xff, 0xa5, 0x0,
    0x0, 0x14, 0x78, 0x88, 0x88, 0x89, 0xce, 0xff,
    0xff, 0xff, 0xa5, 0x0, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x0,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0x0, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x0,
    0x0, 0x38, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x37, 0xcf, 0xff, 0xff, 0xa5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x7c, 0xff, 0xa4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x10, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x26, 0xaa, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x26,
    0xbe, 0xff, 0xfa, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x14, 0x78, 0x88, 0x88, 0x89, 0xce,
    0xff, 0xff, 0xff, 0xa5, 0x0, 0x13, 0x31, 0x0,
    0x0, 0x0, 0x5, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x50, 0x16, 0xbe, 0xea,
    0x51, 0x0, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa5, 0x0, 0x3, 0x8d,
    0xfa, 0x40, 0x0, 0x5, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x50, 0x15, 0xae,
    0xea, 0x51, 0x0, 0x0, 0x38, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x0, 0x24,
    0x42, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x8c, 0xff, 0xff, 0xfa, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x8c, 0xff, 0xa4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xfd,
    0xa5, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x26, 0xaa, 0x72,
    0x0, 0x0, 0x0, 0x12, 0x11, 0x14, 0x8c, 0xfe,
    0x94, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x26, 0xbe, 0xff, 0xfa, 0x50, 0x0,
    0x0, 0x27, 0xcf, 0xda, 0x52, 0x16, 0xbe, 0xe9,
    0x40, 0x0, 0x0, 0x14, 0x78, 0x88, 0x88, 0x89,
    0xce, 0xff, 0xff, 0xff, 0xa5, 0x0, 0x23, 0x32,
    0x1, 0x4a, 0xdf, 0xb6, 0x13, 0x8d, 0xfa, 0x50,
    0x0, 0x5, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x50, 0x15, 0xbe, 0xea, 0x51,
    0x16, 0xbf, 0xc6, 0x13, 0x9e, 0xe9, 0x30, 0x0,
    0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa5, 0x0, 0x3, 0x8d, 0xfa, 0x40, 0x39,
    0xee, 0x83, 0x17, 0xcf, 0xa5, 0x0, 0x5, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x50, 0x15, 0xbe, 0xea, 0x51, 0x16, 0xcf, 0xc6,
    0x13, 0x8e, 0xe9, 0x30, 0x0, 0x38, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x0,
    0x23, 0x31, 0x1, 0x5a, 0xef, 0xb6, 0x13, 0x8d,
    0xfa, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x7c, 0xff, 0xff, 0xfa, 0x50, 0x0, 0x0,
    0x27, 0xcf, 0xda, 0x52, 0x16, 0xbf, 0xe9, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x7c, 0xff, 0xa4, 0x0, 0x0, 0x0, 0x12,
    0x11, 0x15, 0x9d, 0xfe, 0x94, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x11, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b,
    0xfd, 0xa5, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F03E "" */
    0x0, 0x26, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xeb, 0x62, 0x0, 0x0, 0x5a, 0xff,
    0xfd, 0xa7, 0x56, 0x7b, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa5, 0x0, 0x0, 0x5a, 0xff, 0xb5, 0x0, 0x0,
    0x1, 0x7c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xee,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x0, 0x0,
    0x5a, 0xff, 0xeb, 0x63, 0x12, 0x38, 0xcf, 0xff,
    0xff, 0xff, 0xfe, 0xa6, 0x20, 0x13, 0x8c, 0xff,
    0xff, 0xff, 0xa5, 0x0, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xfe, 0xcc, 0xdf, 0xff, 0xff, 0xea, 0x62,
    0x0, 0x0, 0x0, 0x1, 0x38, 0xdf, 0xff, 0xa5,
    0x0, 0x0, 0x5a, 0xff, 0xff, 0xea, 0x62, 0x0,
    0x15, 0x9b, 0xa6, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x8d, 0xff, 0xa5, 0x0, 0x0, 0x5a,
    0xff, 0xe9, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x8d,
    0xff, 0xa5, 0x0, 0x0, 0x5a, 0xff, 0xec, 0x98,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x89, 0xce, 0xff, 0xa5, 0x0,
    0x0, 0x26, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xeb, 0x62, 0x0,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x56,
    0x42, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x6c, 0xff, 0xfb,
    0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x38, 0xdf, 0xff, 0xff, 0xc7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xae, 0xff, 0xff, 0xff, 0xfe, 0x93,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14,
    0x9e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x9e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x83,
    0x0, 0x0, 0x0, 0x3, 0x8d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x72,
    0x0, 0x0, 0x38, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc7, 0x20,
    0x0, 0x4a, 0xff, 0xe9, 0x56, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe8, 0x30, 0x0,
    0x27, 0xdf, 0xfc, 0x73, 0x5a, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb6, 0x10, 0x0, 0x1,
    0x6b, 0xff, 0xfd, 0xa6, 0x43, 0x47, 0xbe, 0xff,
    0xff, 0xff, 0xea, 0x40, 0x0, 0x0, 0x0, 0x1,
    0x48, 0xce, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xb7, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x24, 0x56, 0x77, 0x76, 0x54, 0x20, 0x0,
    0x0, 0x0, 0x0,

    /* U+F048 "" */
    0x25, 0x78, 0x86, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x13, 0x67, 0x52, 0x0, 0x4, 0x9f, 0xff,
    0xc6, 0x10, 0x0, 0x0, 0x0, 0x15, 0x9d, 0xff,
    0xfe, 0x83, 0x0, 0x49, 0xff, 0xfc, 0x61, 0x0,
    0x0, 0x26, 0xae, 0xff, 0xff, 0xff, 0xe9, 0x30,
    0x4, 0x9f, 0xff, 0xc6, 0x10, 0x37, 0xbe, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x93, 0x0, 0x49, 0xff,
    0xfd, 0xa9, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe9, 0x30, 0x4, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x93,
    0x0, 0x49, 0xff, 0xfe, 0xee, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe9, 0x30, 0x4, 0x9f,
    0xff, 0xc6, 0x24, 0x9d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x93, 0x0, 0x49, 0xff, 0xfc, 0x61,
    0x0, 0x3, 0x7c, 0xef, 0xff, 0xff, 0xff, 0xe9,
    0x30, 0x4, 0x9f, 0xff, 0xc6, 0x10, 0x0, 0x0,
    0x2, 0x6b, 0xef, 0xff, 0xfe, 0x93, 0x0, 0x38,
    0xdf, 0xfb, 0x50, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x59, 0xde, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x1, 0x46, 0x76, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x49, 0xff, 0xff, 0xff, 0xda,
    0x74, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xa7, 0x41, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdb, 0x74, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0xb8, 0x52, 0x0,
    0x0, 0x0, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xeb, 0x84, 0x10, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x61, 0x0, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xdb, 0x73, 0x10,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0xb8, 0x42, 0x0,
    0x0, 0x0, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xdb, 0x74, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xa7, 0x41, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49,
    0xff, 0xff, 0xff, 0xda, 0x74, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x36, 0x76, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F04C "" */
    0x0, 0x15, 0xad, 0xef, 0xff, 0xff, 0xfe, 0xc8,
    0x30, 0x0, 0x15, 0xad, 0xef, 0xff, 0xff, 0xfe,
    0xc8, 0x30, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x72, 0x0, 0x5a, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x72, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x82, 0x0, 0x5a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x82, 0x0, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x82, 0x0,
    0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x82,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x82, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x82, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x82, 0x0, 0x5a, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x82, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x82, 0x0, 0x5a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x82, 0x0, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x82, 0x0,
    0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x82,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x82, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x82, 0x0, 0x4a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x72, 0x0, 0x4a, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x72, 0x0, 0x2, 0x47, 0x88,
    0x99, 0x99, 0x88, 0x63, 0x10, 0x0, 0x2, 0x47,
    0x88, 0x99, 0x99, 0x88, 0x63, 0x10,

    /* U+F04D "" */
    0x0, 0x2, 0x47, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x63, 0x10, 0x0, 0x4a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x72, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x82, 0x0, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x82,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x82, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x82, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x82, 0x0, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x82,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x82, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x72, 0x0, 0x15, 0xad, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xc8, 0x30,

    /* U+F051 "" */
    0x2, 0x57, 0x64, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x25, 0x88, 0x85, 0x20, 0x2, 0x7d, 0xff,
    0xfd, 0xa5, 0x20, 0x0, 0x0, 0x0, 0x5, 0xbf,
    0xff, 0xa5, 0x0, 0x28, 0xdf, 0xff, 0xff, 0xfe,
    0xb7, 0x30, 0x0, 0x0, 0x5b, 0xff, 0xfa, 0x50,
    0x2, 0x8d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc8,
    0x31, 0x5, 0xbf, 0xff, 0xa5, 0x0, 0x28, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xda, 0xac,
    0xff, 0xfa, 0x50, 0x2, 0x8d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa5,
    0x0, 0x28, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xee, 0xff, 0xfa, 0x50, 0x2, 0x8d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x94, 0x25,
    0xbf, 0xff, 0xa5, 0x0, 0x28, 0xdf, 0xff, 0xff,
    0xff, 0xfc, 0x84, 0x10, 0x0, 0x5b, 0xff, 0xfa,
    0x50, 0x2, 0x8d, 0xff, 0xff, 0xeb, 0x73, 0x0,
    0x0, 0x0, 0x5, 0xbf, 0xff, 0xa5, 0x0, 0x15,
    0xae, 0xda, 0x62, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5a, 0xef, 0xe9, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x36, 0x77, 0x52, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x38, 0xcf, 0xff, 0xff, 0xea, 0x51,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x26, 0xbe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd9, 0x41, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0xae,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc8, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x14, 0x9d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xb7, 0x20, 0x0,
    0x0, 0x0, 0x2, 0x6c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x94, 0x0, 0x0, 0x0, 0x27, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x50, 0x0,
    0x0, 0x0, 0x1, 0x23, 0x34, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x43,
    0x31, 0x0, 0x0, 0x0, 0x0, 0x38, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x51, 0x0, 0x0,
    0x5, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd8, 0x20, 0x0, 0x0, 0x27, 0xce, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xea, 0x51, 0x0, 0x0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x34, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x25, 0xae, 0xff, 0xfb, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x5a, 0xef, 0xff,
    0xfd, 0x94, 0x10, 0x0, 0x0, 0x0, 0x0, 0x25,
    0xae, 0xff, 0xff, 0xd9, 0x41, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x5a, 0xef, 0xff, 0xfd, 0x94, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x8d, 0xff, 0xff,
    0xeb, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x27, 0xbe, 0xff, 0xff, 0xc8, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x7b,
    0xef, 0xff, 0xfc, 0x83, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x37, 0xce, 0xff, 0xff,
    0xc8, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x7c, 0xef, 0xff, 0xfa, 0x51, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x37,
    0xab, 0x95, 0x10, 0x0,

    /* U+F054 "" */
    0x0, 0x2, 0x34, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x8e, 0xff, 0xfc,
    0x83, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x27, 0xbe, 0xff, 0xff, 0xc8, 0x31, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7b,
    0xef, 0xff, 0xfc, 0x83, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x27, 0xbe, 0xff, 0xff,
    0xc8, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x8d, 0xff, 0xff, 0xfb, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x5a, 0xef, 0xff,
    0xfd, 0x95, 0x10, 0x0, 0x0, 0x0, 0x0, 0x25,
    0xae, 0xff, 0xff, 0xd9, 0x51, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x5a, 0xef, 0xff, 0xfd, 0x95, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x8d, 0xff, 0xff,
    0xd9, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x37, 0xab, 0x85, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x69, 0xa9, 0x84, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x38, 0xdf, 0xff, 0xfb, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x38, 0xef, 0xff, 0xfb, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0xef, 0xff,
    0xfb, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x57, 0x88, 0x88, 0x88, 0x88, 0x9c,
    0xff, 0xff, 0xfd, 0xb8, 0x88, 0x88, 0x88, 0x88,
    0x74, 0x10, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x72, 0x0, 0x15, 0x9b, 0xbb,
    0xbb, 0xbb, 0xbb, 0xce, 0xff, 0xff, 0xfe, 0xdb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xa7, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0xef, 0xff,
    0xfb, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38,
    0xef, 0xff, 0xfb, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x38, 0xef, 0xff, 0xfb, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x15, 0x9c, 0xdd, 0xb7, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x0, 0x2, 0x45, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x53, 0x10, 0x0, 0x4a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x72, 0x0, 0x26, 0xac, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xc8, 0x40,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x58,
    0xab, 0xcd, 0xef, 0xff, 0xff, 0xed, 0xca, 0x97,
    0x42, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x49, 0xcf, 0xff, 0xfe, 0xc9,
    0x64, 0x32, 0x23, 0x45, 0x7a, 0xdf, 0xff, 0xfe,
    0xb7, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x6b, 0xef, 0xff, 0xff, 0xc6, 0x20, 0x0, 0x3,
    0x79, 0x87, 0x52, 0x0, 0x49, 0xdf, 0xff, 0xff,
    0xd9, 0x41, 0x0, 0x0, 0x0, 0x27, 0xcf, 0xff,
    0xff, 0xfe, 0x93, 0x0, 0x0, 0x2, 0x7c, 0xff,
    0xff, 0xea, 0x51, 0x16, 0xbf, 0xff, 0xff, 0xfe,
    0xa5, 0x10, 0x0, 0x49, 0xef, 0xff, 0xff, 0xff,
    0xc6, 0x12, 0x7c, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x40, 0x49, 0xef, 0xff, 0xff, 0xff, 0xc7,
    0x10, 0x0, 0x38, 0xdf, 0xff, 0xff, 0xfe, 0x93,
    0x3, 0x9e, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x61,
    0x16, 0xbf, 0xff, 0xff, 0xfe, 0xa4, 0x10, 0x0,
    0x0, 0x2, 0x6b, 0xef, 0xff, 0xff, 0xc6, 0x20,
    0x26, 0x9b, 0xcc, 0xca, 0x84, 0x11, 0x49, 0xdf,
    0xff, 0xff, 0xd8, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x59, 0xcf, 0xff, 0xfe, 0xc9, 0x64,
    0x32, 0x23, 0x45, 0x7a, 0xdf, 0xff, 0xfe, 0xb7,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x13, 0x57, 0x9b, 0xcd, 0xef, 0xff,
    0xff, 0xed, 0xca, 0x97, 0x42, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x1, 0x35, 0x42, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x8d, 0xff, 0xfd, 0x95, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x8c, 0xef,
    0xff, 0xc8, 0x53, 0x46, 0x8a, 0xbd, 0xee, 0xff,
    0xff, 0xed, 0xcb, 0x97, 0x53, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x59, 0xcf, 0xff, 0xff, 0xff, 0xda, 0x75,
    0x43, 0x23, 0x45, 0x79, 0xdf, 0xff, 0xfe, 0xc8,
    0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x25, 0x9d, 0xff, 0xfe,
    0xb7, 0x44, 0x7a, 0xa9, 0x74, 0x10, 0x28, 0xcf,
    0xff, 0xff, 0xea, 0x51, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x16, 0xbd, 0xc8, 0x41, 0x0, 0x2,
    0x6a, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x72,
    0x4, 0xaf, 0xff, 0xff, 0xff, 0xb6, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x27, 0xdf, 0xff, 0xff, 0xeb,
    0x73, 0x0, 0x0, 0x26, 0xad, 0xff, 0xff, 0xff,
    0xfb, 0x60, 0x27, 0xdf, 0xff, 0xff, 0xff, 0xe8,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x15, 0xbe, 0xff,
    0xff, 0xff, 0xa5, 0x0, 0x0, 0x0, 0x3, 0x7b,
    0xef, 0xff, 0xeb, 0x67, 0xaf, 0xff, 0xff, 0xff,
    0xb6, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x49, 0xdf, 0xff, 0xff, 0xc8, 0x30, 0x0, 0x0,
    0x0, 0x1, 0x37, 0xbe, 0xff, 0xff, 0xff, 0xff,
    0xea, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x37, 0xbe, 0xff, 0xff, 0xc9,
    0x64, 0x32, 0x22, 0x10, 0x0, 0x14, 0x8b, 0xef,
    0xff, 0xea, 0x52, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x57,
    0x9b, 0xcd, 0xef, 0xff, 0xfe, 0xb7, 0x30, 0x0,
    0x1, 0x48, 0xce, 0xff, 0xfc, 0x94, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x14, 0x8c, 0xff, 0xfe,
    0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x45, 0x41, 0x0, 0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x33, 0x21, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x5a, 0xef, 0xff, 0xd8, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xae, 0xff,
    0xff, 0xff, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x38, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb5, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x7c, 0xff, 0xff, 0xee, 0xdd, 0xde, 0xff,
    0xff, 0xea, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xbf,
    0xff, 0xff, 0xd7, 0x20, 0x0, 0x5a, 0xff, 0xff,
    0xfd, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x4a, 0xef, 0xff, 0xff,
    0xfd, 0x72, 0x0, 0x5, 0xaf, 0xff, 0xff, 0xff,
    0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x9d, 0xff, 0xff, 0xff, 0xff, 0xd7,
    0x20, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x27,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xda, 0x87,
    0x8b, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xa4,
    0x10, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc7, 0x20, 0x0, 0x4a,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd9, 0x30,
    0x0, 0x0, 0x15, 0xae, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x95, 0x21, 0x37, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x72, 0x0,
    0x4, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x61, 0x0, 0x1,
    0x46, 0x77, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x87, 0x75, 0x20, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x46,
    0x63, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xaf, 0xff, 0xc8, 0x31,
    0x0, 0x0, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xc8, 0x30, 0x0, 0x0, 0x0, 0x26, 0xbe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x72, 0x0, 0x0,
    0x48, 0xcd, 0xdd, 0xde, 0xff, 0xff, 0xeb, 0x62,
    0x2, 0x6b, 0xef, 0xff, 0xfe, 0xde, 0xff, 0xff,
    0xff, 0xd9, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x26, 0xac, 0xa6, 0x46, 0xbe, 0xff, 0xff,
    0xd9, 0x41, 0x5, 0xaf, 0xfd, 0x95, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x6b, 0xef, 0xff, 0xfd, 0x94, 0x10, 0x0, 0x1,
    0x47, 0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x26, 0xbe, 0xff, 0xff, 0xd8,
    0x55, 0x8b, 0xc8, 0x41, 0x5, 0xaf, 0xfd, 0xa5,
    0x10, 0x0, 0x0, 0x0, 0x48, 0xcd, 0xdd, 0xde,
    0xff, 0xff, 0xfc, 0x84, 0x10, 0x49, 0xdf, 0xff,
    0xfe, 0xde, 0xff, 0xff, 0xff, 0xd9, 0x41, 0x0,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xc8, 0x41,
    0x0, 0x0, 0x0, 0x15, 0xae, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x62, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xaf, 0xff, 0xc8, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x46, 0x63, 0x10, 0x0, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x25, 0x77, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x6b, 0xef, 0xff, 0xfd, 0x94, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x26, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd9, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x6b, 0xef, 0xff, 0xfd, 0x94, 0x12,
    0x6b, 0xef, 0xff, 0xfd, 0x94, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x26, 0xbe, 0xff, 0xff, 0xd9, 0x41,
    0x0, 0x0, 0x0, 0x26, 0xbe, 0xff, 0xff, 0xd9,
    0x41, 0x0, 0x0, 0x16, 0xbf, 0xff, 0xfd, 0x94,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x6b,
    0xef, 0xff, 0xe9, 0x40, 0x0, 0x0, 0x25, 0x77,
    0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x25, 0x77, 0x41, 0x0,

    /* U+F078 "" */
    0x0, 0x0, 0x25, 0x77, 0x41, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x25, 0x77,
    0x41, 0x0, 0x0, 0x16, 0xbf, 0xff, 0xfd, 0x94,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x6b,
    0xef, 0xff, 0xe9, 0x40, 0x0, 0x0, 0x26, 0xbe,
    0xff, 0xff, 0xd9, 0x41, 0x0, 0x0, 0x0, 0x26,
    0xbe, 0xff, 0xff, 0xd9, 0x41, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x6b, 0xef, 0xff, 0xfd, 0x94, 0x12,
    0x6b, 0xef, 0xff, 0xfd, 0x94, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x26, 0xbe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd9, 0x41, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x6b, 0xef, 0xff, 0xfd, 0x94, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x25, 0x76, 0x41, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x38, 0xcf, 0xff,
    0xc8, 0x30, 0x0, 0x3, 0x57, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x76, 0x52, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x38, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xc8, 0x32, 0x5a, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x93,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xaf, 0xff,
    0xcc, 0xcf, 0xfe, 0xcc, 0xdf, 0xff, 0xa4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xef,
    0xe9, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x45, 0x31, 0x49, 0xef, 0xe9, 0x41, 0x35, 0x42,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x9e, 0xfe, 0x94, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x9e, 0xfe, 0x94, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x49, 0xef, 0xe9, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xef, 0xe9,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x7b, 0xb9, 0x55, 0x9e, 0xfe, 0x95, 0x59, 0xbb,
    0x73, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x9e,
    0xff, 0xc9, 0x77, 0x77, 0x77, 0x77, 0x77, 0x76,
    0x53, 0x36, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xb6, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x38, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd7, 0x20, 0x37, 0xbe, 0xff, 0xff,
    0xfe, 0xb7, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x26,
    0xac, 0xa6, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x0, 0x26, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xa6, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xeb,
    0x98, 0x88, 0x88, 0x88, 0x88, 0x88, 0x77, 0x64,
    0x10, 0x0, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa4, 0x0, 0x0,
    0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0x0, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa5,
    0x0, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa5, 0x0, 0x0, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa5, 0x0, 0x0, 0x5a, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x0,
    0x0, 0x26, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xeb, 0x62, 0x0,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x44, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x37, 0xce, 0xff,
    0xec, 0x73, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x7c, 0xef, 0xff, 0xff, 0xff, 0xfe, 0xc7,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x37, 0xce, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xec, 0x73, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x39, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x93, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x8d, 0xff, 0xff, 0xff, 0xd8, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x8d, 0xff,
    0xff, 0xff, 0xd8, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x8d, 0xff, 0xff, 0xff, 0xd8,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x23, 0x33, 0x33, 0x33, 0x32, 0x13,
    0x8d, 0xff, 0xff, 0xff, 0xd8, 0x31, 0x23, 0x33,
    0x33, 0x33, 0x32, 0x10, 0x0, 0x0, 0x4a, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x94, 0x36, 0x99, 0x99,
    0x99, 0x63, 0x49, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xa4, 0x0, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xca, 0x99, 0x99, 0x99, 0xac, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x0, 0x0,
    0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe9, 0x56, 0xbc, 0x75,
    0x8d, 0xff, 0xa5, 0x0, 0x0, 0x25, 0x9a, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8, 0x52,
    0x0,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x35, 0x76,
    0x53, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x27, 0xcf, 0xff, 0xff, 0xff, 0xfe,
    0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x9e,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x93, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x49, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x5a, 0xdf, 0xff, 0xff, 0xff, 0xe9, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49,
    0xef, 0xff, 0xfe, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x27, 0xcf, 0xff, 0xff, 0xd7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x11, 0x0, 0x0, 0x0, 0x0, 0x1, 0x49,
    0xdf, 0xff, 0xff, 0xe9, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x13, 0x58, 0xac, 0xef, 0xfe, 0xb5,
    0x10, 0x1, 0x36, 0xad, 0xff, 0xff, 0xff, 0xc7,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xdc, 0xce, 0xff,
    0xff, 0xff, 0xfc, 0x94, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x17, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xa6, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x8d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xed,
    0xb9, 0x63, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0x77,
    0x76, 0x65, 0x43, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C4 "" */
    0x0, 0x0, 0x2, 0x57, 0x89, 0x98, 0x63, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x23, 0x32,
    0x10, 0x0, 0x0, 0x15, 0xae, 0xff, 0xff, 0xff,
    0xff, 0xd7, 0x20, 0x0, 0x0, 0x1, 0x48, 0xcf,
    0xff, 0xff, 0xea, 0x50, 0x0, 0x49, 0xff, 0xe9,
    0x40, 0x17, 0xcf, 0xfc, 0x71, 0x0, 0x14, 0x8c,
    0xff, 0xff, 0xff, 0xd9, 0x51, 0x0, 0x0, 0x17,
    0xcf, 0xff, 0xdb, 0xce, 0xff, 0xfd, 0x84, 0x48,
    0xcf, 0xff, 0xff, 0xfd, 0x95, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x14, 0x8a, 0xcd, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd9, 0x51, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x9e, 0xff, 0xff, 0xff, 0xff, 0xc7, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x57,
    0x89, 0xbd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xb6, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15,
    0xae, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xa7, 0x8b,
    0xef, 0xff, 0xff, 0xeb, 0x62, 0x0, 0x0, 0x0,
    0x0, 0x49, 0xff, 0xe9, 0x40, 0x17, 0xcf, 0xfc,
    0x71, 0x0, 0x27, 0xbe, 0xff, 0xff, 0xfe, 0xb6,
    0x20, 0x0, 0x0, 0x17, 0xcf, 0xff, 0xdb, 0xce,
    0xff, 0xe9, 0x40, 0x0, 0x0, 0x2, 0x6b, 0xef,
    0xff, 0xff, 0xe9, 0x40, 0x0, 0x0, 0x14, 0x8a,
    0xcc, 0xcb, 0x96, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x13, 0x56, 0x66, 0x41, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x23, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x31, 0x12, 0x21, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x49,
    0xed, 0x94, 0x10, 0x0, 0x0, 0x1, 0x23, 0x44,
    0x32, 0x15, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa5, 0x49, 0xef, 0xfe, 0xc7, 0x20, 0x0, 0x4a,
    0xff, 0xff, 0xe9, 0x45, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xec, 0x98, 0x88, 0x88, 0x85, 0x20,
    0x0, 0x5a, 0xff, 0xff, 0xe9, 0x45, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x50, 0x0, 0x5a, 0xff, 0xff, 0xe9, 0x45,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x50, 0x0, 0x5a, 0xff, 0xff,
    0xe9, 0x45, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x50, 0x0, 0x5a,
    0xff, 0xff, 0xe9, 0x45, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x50,
    0x0, 0x5a, 0xff, 0xff, 0xe9, 0x45, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x50, 0x0, 0x5a, 0xff, 0xff, 0xe9, 0x45,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x40, 0x0, 0x5a, 0xff, 0xff,
    0xfd, 0x95, 0x33, 0x34, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x43, 0x21, 0x0, 0x0, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x14, 0x67, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x76, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F0C7 "" */
    0x0, 0x2, 0x47, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x87, 0x52, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xb6, 0x20, 0x0, 0x0, 0x0, 0x5a, 0xff, 0xd8,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x6b, 0xff, 0xff, 0xeb, 0x62, 0x0, 0x0, 0x5a,
    0xff, 0xd8, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6b, 0xff, 0xff, 0xff, 0xe9, 0x30,
    0x0, 0x5a, 0xff, 0xeb, 0x97, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0xad, 0xff, 0xff, 0xff,
    0xf9, 0x40, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x40, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd9, 0x42, 0x12, 0x5a, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x40, 0x0, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x72, 0x0, 0x0,
    0x3, 0x8d, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x40,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x94,
    0x0, 0x0, 0x5, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x40, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xca, 0x9a, 0xce, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x40, 0x0, 0x16, 0xad, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xed, 0x95, 0x10,

    /* U+F0C9 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x72, 0x0, 0x26, 0x9a, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xa7, 0x41, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x25, 0x88, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x86, 0x31, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x72, 0x0, 0x0, 0x11, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x11, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x26, 0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xa7, 0x41, 0x0, 0x4a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0E0 "" */
    0x0, 0x26, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xeb, 0x62, 0x0, 0x0, 0x49, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa4, 0x0, 0x0, 0x1, 0x47, 0xbe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xeb, 0x74, 0x10, 0x0, 0x0,
    0x59, 0xc9, 0x53, 0x47, 0xbe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xeb, 0x74,
    0x35, 0x9c, 0x95, 0x0, 0x0, 0x5a, 0xff, 0xff,
    0xd9, 0x53, 0x47, 0xbe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xeb, 0x74, 0x35, 0x9c, 0xff, 0xff, 0xa5,
    0x0, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xd9,
    0x63, 0x36, 0xad, 0xee, 0xda, 0x63, 0x36, 0x9d,
    0xff, 0xff, 0xff, 0xff, 0xa5, 0x0, 0x0, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xda, 0x75,
    0x44, 0x57, 0xad, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa5, 0x0, 0x0, 0x5a, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x0,
    0x0, 0x26, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xeb, 0x62, 0x0,

    /* U+F0E7 "" */
    0x0, 0x0, 0x15, 0x9b, 0xbb, 0xbb, 0xbb, 0xbb,
    0xa8, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb6, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x7c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb6, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x49, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x16,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xb6, 0x10, 0x3, 0x8e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc6,
    0x10, 0x0, 0x49, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x82, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x27, 0xcf, 0xff, 0xff,
    0xe9, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xaf, 0xff, 0xfe, 0xa5, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x8e,
    0xff, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x7c, 0xff, 0xd8, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5a, 0xfe, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x23,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x25, 0x8a, 0xa8,
    0x52, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x38, 0xef, 0xff, 0xff, 0xff,
    0xb8, 0x8c, 0xff, 0xff, 0xff, 0xfc, 0x72, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xff, 0xb8, 0x8c, 0xff, 0xff, 0xff, 0xff,
    0x94, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xdc, 0xbb, 0xbb,
    0xbb, 0xba, 0x73, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0xfc, 0x74, 0x58,
    0x99, 0x99, 0x99, 0x99, 0x52, 0x25, 0x64, 0x10,
    0x0, 0x0, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xfa,
    0x55, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x94, 0x4a,
    0xff, 0xda, 0x52, 0x0, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xfa, 0x55, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xa5, 0x23, 0x56, 0x66, 0x53, 0x10, 0x0, 0x5a,
    0xff, 0xff, 0xff, 0xfa, 0x55, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0x40,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0xfa, 0x55, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x40, 0x0, 0x49, 0xef, 0xff, 0xff, 0xfa,
    0x55, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x23,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x31, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x14, 0x65, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x9d, 0xff, 0xb6, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x26, 0xad, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xc9, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x49, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x72, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd8, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x28, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x8d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa5, 0x0, 0x0,
    0x0, 0x2, 0x6b, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x93, 0x0, 0x0, 0x39, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x61, 0x0, 0x1, 0x23, 0x34,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x43, 0x21, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x7c, 0xff, 0xff,
    0xfe, 0x94, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x46, 0x76, 0x52, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F11C "" */
    0x0, 0x26, 0xbe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x84, 0x0, 0x5,
    0xaf, 0xfe, 0xc9, 0x88, 0xad, 0xeb, 0x88, 0x8b,
    0xed, 0xa8, 0x8b, 0xee, 0xb8, 0x88, 0x8b, 0xee,
    0xb8, 0x8a, 0xdf, 0xfd, 0x72, 0x0, 0x5a, 0xff,
    0xd8, 0x30, 0x5, 0xbc, 0x71, 0x1, 0x7c, 0xb5,
    0x1, 0x6b, 0xb6, 0x0, 0x0, 0x6b, 0xb6, 0x10,
    0x5b, 0xff, 0xd8, 0x20, 0x5, 0xaf, 0xff, 0xff,
    0xfe, 0xdc, 0xcd, 0xef, 0xed, 0xcc, 0xde, 0xed,
    0xcc, 0xce, 0xff, 0xec, 0xcc, 0xef, 0xff, 0xff,
    0xfd, 0x82, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xa4,
    0x0, 0x38, 0xc8, 0x30, 0x5, 0xaa, 0x40, 0x2,
    0x7d, 0xd7, 0x20, 0x27, 0xcf, 0xff, 0xff, 0xd8,
    0x20, 0x5, 0xaf, 0xff, 0xff, 0xfe, 0xdc, 0xcd,
    0xef, 0xed, 0xcc, 0xde, 0xed, 0xcc, 0xce, 0xff,
    0xec, 0xcc, 0xef, 0xff, 0xff, 0xfd, 0x82, 0x0,
    0x5a, 0xff, 0xd8, 0x30, 0x5, 0xbc, 0x71, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6b,
    0xb6, 0x10, 0x5b, 0xff, 0xd8, 0x20, 0x5, 0xaf,
    0xfe, 0xc9, 0x88, 0xad, 0xeb, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x8b, 0xde, 0xb8,
    0x8a, 0xdf, 0xfd, 0x72, 0x0, 0x26, 0xbe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x84, 0x0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x24, 0x66, 0x53, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x35, 0x8a, 0xde, 0xff, 0xff, 0xfe,
    0x93, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x47, 0x9c, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x61, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x68, 0xad,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0, 0x24,
    0x79, 0xce, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc7, 0x20, 0x0,
    0x0, 0x0, 0x26, 0xbe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x50, 0x0, 0x0, 0x0, 0x0, 0x38,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x83, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x33, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x9d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x6c, 0xff, 0xff, 0xff, 0xff, 0xea, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6c, 0xff,
    0xff, 0xff, 0xfd, 0x82, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x6c, 0xff, 0xff, 0xff, 0xb5,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6b, 0xff, 0xff, 0xe9, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x46, 0x64,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F15B "" */
    0x0, 0x26, 0x9b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xba, 0x62, 0x36, 0x74, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe9, 0x45, 0xaf, 0xfd, 0x94, 0x10, 0x0,
    0x0, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x94, 0x5a, 0xff, 0xff, 0xfd, 0x94,
    0x10, 0x0, 0x5, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x52, 0x34, 0x44, 0x44, 0x44,
    0x31, 0x0, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x50, 0x0, 0x5, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa5, 0x0, 0x0, 0x5a, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x50, 0x0, 0x5, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0x0, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x50, 0x0, 0x5, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa5, 0x0, 0x0, 0x5a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x50, 0x0, 0x5, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa5, 0x0, 0x0, 0x2,
    0x34, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x31, 0x0, 0x0,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x35, 0x67, 0x99, 0xab, 0xbb, 0xcb, 0xbb,
    0xa9, 0x97, 0x65, 0x31, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x25,
    0x8b, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdb, 0x85,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x7b,
    0xef, 0xff, 0xff, 0xfe, 0xca, 0x87, 0x54, 0x33,
    0x22, 0x22, 0x23, 0x34, 0x57, 0x8a, 0xce, 0xff,
    0xff, 0xff, 0xeb, 0x73, 0x10, 0x0, 0x0, 0x28,
    0xdf, 0xff, 0xfd, 0xa7, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x37, 0xad, 0xff, 0xff, 0xd8, 0x30, 0x0,
    0x0, 0x2, 0x57, 0x63, 0x0, 0x0, 0x1, 0x25,
    0x79, 0xbc, 0xde, 0xff, 0xff, 0xfe, 0xdc, 0xb9,
    0x75, 0x21, 0x0, 0x0, 0x3, 0x57, 0x52, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x8c,
    0xef, 0xff, 0xff, 0xff, 0xfe, 0xee, 0xee, 0xff,
    0xff, 0xff, 0xff, 0xec, 0x84, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x6b, 0xef, 0xfc, 0x96, 0x42, 0x10, 0x0, 0x0,
    0x0, 0x12, 0x46, 0x9c, 0xef, 0xeb, 0x62, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x11, 0x10, 0x0, 0x0, 0x0, 0x1, 0x21, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x5a, 0xef, 0xff, 0xea, 0x51, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x49, 0xff, 0xff, 0xff, 0xf9, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x26, 0xac, 0xdc, 0xa6, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F240 "" */
    0x0, 0x1, 0x35, 0x67, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x65, 0x20, 0x0,
    0x0, 0x0, 0x4, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x83, 0x0, 0x0, 0x0, 0x5a, 0xff, 0xd8, 0x32,
    0x34, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x21, 0x49,
    0xef, 0xff, 0xd8, 0x30, 0x0, 0x5, 0xaf, 0xfd,
    0x84, 0x6c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x52, 0x59, 0xce, 0xff, 0xa5, 0x0, 0x0, 0x5a,
    0xff, 0xd8, 0x46, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa5, 0x0, 0x28, 0xdf, 0xfa, 0x50, 0x0,
    0x5, 0xaf, 0xfd, 0x83, 0x59, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xc8, 0x43, 0x8e, 0xff, 0xff, 0xa5,
    0x0, 0x0, 0x5a, 0xff, 0xeb, 0x87, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x9c, 0xff, 0xfc,
    0x94, 0x10, 0x0, 0x2, 0x6c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xea, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F241 "" */
    0x0, 0x1, 0x35, 0x67, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x65, 0x20, 0x0,
    0x0, 0x0, 0x4, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x83, 0x0, 0x0, 0x0, 0x5a, 0xff, 0xd8, 0x32,
    0x34, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x43, 0x21, 0x0, 0x0, 0x0, 0x49,
    0xef, 0xff, 0xd8, 0x30, 0x0, 0x5, 0xaf, 0xfd,
    0x84, 0x6c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc6, 0x10, 0x0, 0x0,
    0x2, 0x59, 0xce, 0xff, 0xa5, 0x0, 0x0, 0x5a,
    0xff, 0xd8, 0x46, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x28, 0xdf, 0xfa, 0x50, 0x0,
    0x5, 0xaf, 0xfd, 0x83, 0x59, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x95,
    0x0, 0x0, 0x0, 0x3, 0x8e, 0xff, 0xff, 0xa5,
    0x0, 0x0, 0x5a, 0xff, 0xeb, 0x87, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x9c, 0xff, 0xfc,
    0x94, 0x10, 0x0, 0x2, 0x6c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xea, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F242 "" */
    0x0, 0x1, 0x35, 0x67, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x65, 0x20, 0x0,
    0x0, 0x0, 0x4, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x83, 0x0, 0x0, 0x0, 0x5a, 0xff, 0xd8, 0x32,
    0x34, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x32,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49,
    0xef, 0xff, 0xd8, 0x30, 0x0, 0x5, 0xaf, 0xfd,
    0x84, 0x6c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x82, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x59, 0xce, 0xff, 0xa5, 0x0, 0x0, 0x5a,
    0xff, 0xd8, 0x46, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd8, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x28, 0xdf, 0xfa, 0x50, 0x0,
    0x5, 0xaf, 0xfd, 0x83, 0x59, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xca, 0x62, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x8e, 0xff, 0xff, 0xa5,
    0x0, 0x0, 0x5a, 0xff, 0xeb, 0x87, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x9c, 0xff, 0xfc,
    0x94, 0x10, 0x0, 0x2, 0x6c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xea, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F243 "" */
    0x0, 0x1, 0x35, 0x67, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x65, 0x20, 0x0,
    0x0, 0x0, 0x4, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x83, 0x0, 0x0, 0x0, 0x5a, 0xff, 0xd8, 0x32,
    0x34, 0x44, 0x44, 0x43, 0x21, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49,
    0xef, 0xff, 0xd8, 0x30, 0x0, 0x5, 0xaf, 0xfd,
    0x84, 0x6c, 0xff, 0xff, 0xff, 0xe9, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x59, 0xce, 0xff, 0xa5, 0x0, 0x0, 0x5a,
    0xff, 0xd8, 0x46, 0xcf, 0xff, 0xff, 0xfe, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x28, 0xdf, 0xfa, 0x50, 0x0,
    0x5, 0xaf, 0xfd, 0x83, 0x59, 0xcc, 0xcc, 0xcc,
    0xb7, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x8e, 0xff, 0xff, 0xa5,
    0x0, 0x0, 0x5a, 0xff, 0xeb, 0x87, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x9c, 0xff, 0xfc,
    0x94, 0x10, 0x0, 0x2, 0x6c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xea, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F244 "" */
    0x0, 0x1, 0x35, 0x67, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x65, 0x20, 0x0,
    0x0, 0x0, 0x4, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x83, 0x0, 0x0, 0x0, 0x5a, 0xff, 0xd8, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49,
    0xef, 0xff, 0xd8, 0x30, 0x0, 0x5, 0xaf, 0xfd,
    0x82, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x59, 0xce, 0xff, 0xa5, 0x0, 0x0, 0x5a,
    0xff, 0xd8, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x28, 0xdf, 0xfa, 0x50, 0x0,
    0x5, 0xaf, 0xfd, 0x82, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x8e, 0xff, 0xff, 0xa5,
    0x0, 0x0, 0x5a, 0xff, 0xeb, 0x87, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x9c, 0xff, 0xfc,
    0x94, 0x10, 0x0, 0x2, 0x6c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xea, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x12, 0x33, 0x59, 0xdf,
    0xfe, 0xb6, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x15, 0xad, 0xca, 0x9b,
    0xdf, 0xff, 0xfe, 0x93, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x11, 0x10, 0x0, 0x0, 0x0, 0x38, 0xdb, 0x51,
    0x0, 0x1, 0x24, 0x54, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x9d, 0xff, 0xff, 0xd9, 0x41, 0x1, 0x6b, 0xd8,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x6a, 0xb8, 0x52, 0x0, 0x0, 0x0,
    0x4, 0xaf, 0xff, 0xff, 0xff, 0xfe, 0xed, 0xee,
    0xee, 0xee, 0xee, 0xed, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xde, 0xff, 0xff, 0xfc, 0x72,
    0x0, 0x0, 0x4, 0x9d, 0xff, 0xff, 0xd9, 0x41,
    0x0, 0x0, 0x0, 0x16, 0xbd, 0x83, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x6a, 0xb8, 0x52,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x8c, 0xb6,
    0x10, 0x3, 0x57, 0x77, 0x77, 0x42, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x49, 0xcc, 0xba, 0xce, 0xff, 0xff, 0xfb, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x12, 0x49, 0xdf, 0xff, 0xff,
    0xb5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x23, 0x34,
    0x43, 0x32, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x6a, 0xde, 0xff, 0xfe,
    0xee, 0xff, 0xff, 0xed, 0xa5, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x4a, 0xef, 0xff, 0xff, 0xff,
    0xa5, 0x37, 0xcf, 0xff, 0xff, 0xfd, 0x93, 0x0,
    0x0, 0x0, 0x1, 0x7c, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x40, 0x1, 0x48, 0xdf, 0xff, 0xff, 0xa5,
    0x0, 0x0, 0x0, 0x5b, 0xff, 0xfd, 0x84, 0x59,
    0xdf, 0xa4, 0x27, 0xa8, 0x42, 0x5b, 0xff, 0xfe,
    0x83, 0x0, 0x0, 0x28, 0xdf, 0xff, 0xff, 0xc7,
    0x32, 0x44, 0x21, 0x34, 0x33, 0x7c, 0xff, 0xff,
    0xfa, 0x50, 0x0, 0x3, 0x8e, 0xff, 0xff, 0xff,
    0xff, 0xc6, 0x20, 0x1, 0x5a, 0xef, 0xff, 0xff,
    0xff, 0xb6, 0x0, 0x0, 0x38, 0xef, 0xff, 0xff,
    0xfe, 0xb6, 0x20, 0x0, 0x1, 0x49, 0xdf, 0xff,
    0xff, 0xfb, 0x60, 0x0, 0x2, 0x7c, 0xff, 0xfe,
    0xb6, 0x22, 0x59, 0x84, 0x26, 0x97, 0x32, 0x5a,
    0xef, 0xff, 0xa5, 0x0, 0x0, 0x4, 0x9f, 0xff,
    0xec, 0xab, 0xdf, 0xfa, 0x41, 0x46, 0x42, 0x59,
    0xdf, 0xff, 0xd8, 0x20, 0x0, 0x0, 0x4, 0x9e,
    0xff, 0xff, 0xff, 0xff, 0xa4, 0x1, 0x5a, 0xdf,
    0xff, 0xff, 0xd8, 0x20, 0x0, 0x0, 0x0, 0x1,
    0x49, 0xdf, 0xff, 0xff, 0xfb, 0xaa, 0xdf, 0xff,
    0xff, 0xfd, 0x94, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x24, 0x68, 0x9a, 0xab, 0xbb, 0xa9,
    0x87, 0x52, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x67,
    0x88, 0x88, 0x87, 0x75, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x37, 0xbb, 0xcc, 0xcc, 0xcc,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xed, 0xcc,
    0xcc, 0xcc, 0xb9, 0x51, 0x0, 0x37, 0xbb, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xb9, 0x51, 0x0, 0x0,
    0x24, 0x78, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x86, 0x30, 0x0,
    0x0, 0x0, 0x49, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x61, 0x0, 0x0, 0x0, 0x49, 0xef, 0xff, 0xa5,
    0x5a, 0xff, 0xe9, 0x56, 0xbf, 0xfd, 0x85, 0x8d,
    0xff, 0xfc, 0x61, 0x0, 0x0, 0x0, 0x49, 0xef,
    0xff, 0xa4, 0x4a, 0xff, 0xe8, 0x46, 0xbf, 0xfc,
    0x74, 0x7c, 0xff, 0xfc, 0x61, 0x0, 0x0, 0x0,
    0x49, 0xef, 0xff, 0xa4, 0x4a, 0xff, 0xe8, 0x46,
    0xbf, 0xfc, 0x74, 0x7c, 0xff, 0xfc, 0x61, 0x0,
    0x0, 0x0, 0x49, 0xef, 0xff, 0xa4, 0x4a, 0xff,
    0xe8, 0x46, 0xbf, 0xfc, 0x74, 0x7c, 0xff, 0xfc,
    0x61, 0x0, 0x0, 0x0, 0x49, 0xef, 0xff, 0xa4,
    0x4a, 0xff, 0xe8, 0x46, 0xbf, 0xfc, 0x74, 0x7c,
    0xff, 0xfc, 0x61, 0x0, 0x0, 0x0, 0x49, 0xef,
    0xff, 0xa5, 0x5a, 0xff, 0xe9, 0x56, 0xbf, 0xfd,
    0x85, 0x8d, 0xff, 0xfc, 0x61, 0x0, 0x0, 0x0,
    0x38, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x60, 0x0,
    0x0, 0x0, 0x1, 0x36, 0x77, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x87, 0x76, 0x42,
    0x0, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x67,
    0x63, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x5a, 0xef, 0xff, 0xff, 0xd9, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x25, 0x9d,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x72, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x5a, 0xef, 0xda, 0x53, 0x5a, 0xdf, 0xff,
    0xff, 0xfd, 0x82, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x25, 0xae, 0xff, 0xff,
    0xff, 0xfd, 0xa5, 0x35, 0xad, 0xda, 0x52, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x5a, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd8, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x25, 0xae, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xea, 0x52, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x5a,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xa5, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x25, 0xae, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xea, 0x52, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xa5,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x28, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xea, 0x52, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4a, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xa5, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x67, 0x76,
    0x55, 0x44, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0x9c,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xec, 0x84,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0xad,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa4, 0x0, 0x0, 0x0, 0x0, 0x15, 0xad,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x94, 0x1,
    0x49, 0xdf, 0xfd, 0x94, 0x10, 0x39, 0xdf, 0xff,
    0xff, 0xff, 0xfa, 0x50, 0x0, 0x0, 0x15, 0xad,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x94, 0x10, 0x1, 0x22, 0x10, 0x1, 0x49, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xa5, 0x0, 0x0, 0x39,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x61, 0x0, 0x0, 0x15, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x50, 0x0,
    0x0, 0x15, 0xad, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x94, 0x10, 0x1, 0x33, 0x10,
    0x1, 0x49, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xa5,
    0x0, 0x0, 0x0, 0x0, 0x15, 0xad, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x94, 0x11, 0x49, 0xdf,
    0xfd, 0x94, 0x10, 0x49, 0xdf, 0xff, 0xff, 0xff,
    0xfa, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15,
    0x9d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x14, 0x9c, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xec, 0x84, 0x0, 0x0,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x67, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x77, 0x64, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x25, 0xad, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x83,
    0x0, 0x0, 0x0, 0x26, 0xbe, 0xff, 0xa4, 0x0,
    0x5b, 0xb6, 0x3, 0x9b, 0x72, 0x28, 0xdf, 0xfa,
    0x50, 0x0, 0x4, 0x9e, 0xff, 0xff, 0xfa, 0x40,
    0x5, 0xbb, 0x60, 0x39, 0xb7, 0x22, 0x8d, 0xff,
    0xa5, 0x0, 0x0, 0x5a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x50, 0x0, 0x5, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa5, 0x0, 0x0, 0x5a, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x50, 0x0, 0x5, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0x0, 0x0, 0x5a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x50, 0x0, 0x5, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa5, 0x0, 0x0, 0x5a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x50, 0x0, 0x2, 0x6c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x62, 0x0, 0x0, 0x0,
    0x1, 0x34, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x31, 0x0, 0x0, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x12, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x48, 0xdf,
    0xb6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x6a,
    0xc9, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x27, 0xdf, 0xff, 0xb6, 0x0, 0x0,
    0x0, 0x0, 0x37, 0xce, 0xff, 0xfc, 0x72, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x28,
    0xdf, 0xff, 0xb6, 0x0, 0x0, 0x3, 0x8c, 0xff,
    0xff, 0xff, 0xff, 0xed, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xde, 0xff, 0xff, 0xb6,
    0x0, 0x0, 0x16, 0xbe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa4, 0x0, 0x0, 0x0,
    0x2, 0x6b, 0xef, 0xff, 0xfc, 0x72, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15,
    0xad, 0xfc, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 52, .box_w = 6, .box_h = 0, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 51, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 41, .adv_w = 75, .box_w = 15, .box_h = 4, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 71, .adv_w = 135, .box_w = 27, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 193, .adv_w = 119, .box_w = 24, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 349, .adv_w = 162, .box_w = 30, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 484, .adv_w = 132, .box_w = 27, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 619, .adv_w = 40, .box_w = 9, .box_h = 4, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 637, .adv_w = 65, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 715, .adv_w = 65, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 793, .adv_w = 77, .box_w = 18, .box_h = 5, .ofs_x = -1, .ofs_y = 5},
    {.bitmap_index = 838, .adv_w = 112, .box_w = 21, .box_h = 6, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 901, .adv_w = 44, .box_w = 9, .box_h = 4, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 919, .adv_w = 74, .box_w = 15, .box_h = 2, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 934, .adv_w = 44, .box_w = 9, .box_h = 2, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 943, .adv_w = 68, .box_w = 18, .box_h = 13, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 1060, .adv_w = 128, .box_w = 24, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1168, .adv_w = 71, .box_w = 15, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1236, .adv_w = 110, .box_w = 24, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1344, .adv_w = 110, .box_w = 24, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1452, .adv_w = 128, .box_w = 27, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1574, .adv_w = 110, .box_w = 24, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1682, .adv_w = 118, .box_w = 24, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1790, .adv_w = 115, .box_w = 24, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1898, .adv_w = 124, .box_w = 24, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2006, .adv_w = 118, .box_w = 24, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2114, .adv_w = 44, .box_w = 9, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2146, .adv_w = 44, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2187, .adv_w = 112, .box_w = 21, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 2261, .adv_w = 112, .box_w = 21, .box_h = 5, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 2314, .adv_w = 112, .box_w = 21, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 2388, .adv_w = 110, .box_w = 24, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2496, .adv_w = 199, .box_w = 39, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2730, .adv_w = 141, .box_w = 33, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2879, .adv_w = 145, .box_w = 27, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3001, .adv_w = 139, .box_w = 27, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3123, .adv_w = 159, .box_w = 30, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3258, .adv_w = 129, .box_w = 24, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3366, .adv_w = 122, .box_w = 24, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3474, .adv_w = 148, .box_w = 27, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3596, .adv_w = 156, .box_w = 27, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3718, .adv_w = 60, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3759, .adv_w = 98, .box_w = 21, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3854, .adv_w = 138, .box_w = 27, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3976, .adv_w = 114, .box_w = 24, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4084, .adv_w = 183, .box_w = 33, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4233, .adv_w = 156, .box_w = 27, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4355, .adv_w = 161, .box_w = 30, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4490, .adv_w = 139, .box_w = 27, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4612, .adv_w = 161, .box_w = 33, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4810, .adv_w = 140, .box_w = 27, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4932, .adv_w = 119, .box_w = 24, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5040, .adv_w = 113, .box_w = 27, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5162, .adv_w = 152, .box_w = 27, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5284, .adv_w = 137, .box_w = 30, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5419, .adv_w = 216, .box_w = 42, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5608, .adv_w = 129, .box_w = 30, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5743, .adv_w = 124, .box_w = 30, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5878, .adv_w = 126, .box_w = 24, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5986, .adv_w = 64, .box_w = 15, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6084, .adv_w = 68, .box_w = 18, .box_h = 13, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 6201, .adv_w = 64, .box_w = 15, .box_h = 13, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 6299, .adv_w = 112, .box_w = 21, .box_h = 6, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 6362, .adv_w = 96, .box_w = 24, .box_h = 1, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 6374, .adv_w = 115, .box_w = 12, .box_h = 2, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 6386, .adv_w = 115, .box_w = 21, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6460, .adv_w = 131, .box_w = 27, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6595, .adv_w = 110, .box_w = 21, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6669, .adv_w = 131, .box_w = 24, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6789, .adv_w = 118, .box_w = 24, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6873, .adv_w = 68, .box_w = 18, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 6963, .adv_w = 132, .box_w = 24, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7083, .adv_w = 131, .box_w = 24, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7203, .adv_w = 54, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7248, .adv_w = 55, .box_w = 15, .box_h = 13, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 7346, .adv_w = 118, .box_w = 24, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7466, .adv_w = 54, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7511, .adv_w = 203, .box_w = 36, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7637, .adv_w = 131, .box_w = 24, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7721, .adv_w = 122, .box_w = 24, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7805, .adv_w = 131, .box_w = 27, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7940, .adv_w = 131, .box_w = 24, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8060, .adv_w = 79, .box_w = 15, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8113, .adv_w = 96, .box_w = 21, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8187, .adv_w = 79, .box_w = 18, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8268, .adv_w = 130, .box_w = 24, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8352, .adv_w = 107, .box_w = 27, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8447, .adv_w = 173, .box_w = 39, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8584, .adv_w = 106, .box_w = 24, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8668, .adv_w = 107, .box_w = 27, .box_h = 10, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 8803, .adv_w = 100, .box_w = 21, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8877, .adv_w = 67, .box_w = 15, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8975, .adv_w = 57, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9034, .adv_w = 67, .box_w = 15, .box_h = 13, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 9132, .adv_w = 112, .box_w = 21, .box_h = 2, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 9153, .adv_w = 80, .box_w = 15, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 9191, .adv_w = 60, .box_w = 12, .box_h = 3, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 9209, .adv_w = 192, .box_w = 42, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 9482, .adv_w = 192, .box_w = 42, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 9671, .adv_w = 192, .box_w = 42, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 9902, .adv_w = 192, .box_w = 42, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 10091, .adv_w = 132, .box_w = 30, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 10226, .adv_w = 192, .box_w = 42, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 10499, .adv_w = 192, .box_w = 36, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10733, .adv_w = 216, .box_w = 45, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 10981, .adv_w = 192, .box_w = 42, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 11254, .adv_w = 216, .box_w = 45, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 11457, .adv_w = 192, .box_w = 42, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 11730, .adv_w = 96, .box_w = 24, .box_h = 10, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 11850, .adv_w = 144, .box_w = 33, .box_h = 10, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 12015, .adv_w = 216, .box_w = 45, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 12308, .adv_w = 192, .box_w = 42, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 12497, .adv_w = 132, .box_w = 30, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 12692, .adv_w = 168, .box_w = 27, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 12854, .adv_w = 168, .box_w = 36, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 13088, .adv_w = 168, .box_w = 36, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 13286, .adv_w = 168, .box_w = 36, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 13484, .adv_w = 168, .box_w = 27, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 13646, .adv_w = 168, .box_w = 39, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 13861, .adv_w = 120, .box_w = 24, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 13993, .adv_w = 120, .box_w = 24, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 14125, .adv_w = 168, .box_w = 36, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 14323, .adv_w = 168, .box_w = 36, .box_h = 3, .ofs_x = -1, .ofs_y = 3},
    {.bitmap_index = 14377, .adv_w = 216, .box_w = 45, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 14580, .adv_w = 240, .box_w = 51, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 14912, .adv_w = 216, .box_w = 45, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 15205, .adv_w = 192, .box_w = 42, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 15436, .adv_w = 168, .box_w = 36, .box_h = 7, .ofs_x = -1, .ofs_y = 1},
    {.bitmap_index = 15562, .adv_w = 168, .box_w = 36, .box_h = 7, .ofs_x = -1, .ofs_y = 1},
    {.bitmap_index = 15688, .adv_w = 240, .box_w = 51, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 15943, .adv_w = 192, .box_w = 42, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 16132, .adv_w = 192, .box_w = 42, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 16405, .adv_w = 192, .box_w = 42, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 16678, .adv_w = 168, .box_w = 36, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 16876, .adv_w = 168, .box_w = 36, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 17110, .adv_w = 168, .box_w = 36, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 17308, .adv_w = 168, .box_w = 36, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 17506, .adv_w = 192, .box_w = 42, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 17695, .adv_w = 120, .box_w = 27, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 17871, .adv_w = 168, .box_w = 36, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 18105, .adv_w = 168, .box_w = 36, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 18339, .adv_w = 216, .box_w = 45, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18542, .adv_w = 192, .box_w = 42, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 18815, .adv_w = 144, .box_w = 33, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 19030, .adv_w = 240, .box_w = 51, .box_h = 12, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 19336, .adv_w = 240, .box_w = 51, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 19566, .adv_w = 240, .box_w = 51, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 19796, .adv_w = 240, .box_w = 51, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 20026, .adv_w = 240, .box_w = 51, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 20256, .adv_w = 240, .box_w = 51, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 20486, .adv_w = 240, .box_w = 51, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 20767, .adv_w = 168, .box_w = 33, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 20982, .adv_w = 168, .box_w = 36, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 21216, .adv_w = 192, .box_w = 42, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 21489, .adv_w = 240, .box_w = 51, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 21719, .adv_w = 144, .box_w = 33, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 21934, .adv_w = 193, .box_w = 42, .box_h = 9, .ofs_x = -1, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1f72, 0xef51, 0xef58, 0xef5b, 0xef5c, 0xef5d, 0xef61,
    0xef63, 0xef65, 0xef69, 0xef6c, 0xef71, 0xef76, 0xef77, 0xef78,
    0xef8e, 0xef93, 0xef98, 0xef9b, 0xef9c, 0xef9d, 0xefa1, 0xefa2,
    0xefa3, 0xefa4, 0xefb7, 0xefb8, 0xefbe, 0xefc0, 0xefc1, 0xefc4,
    0xefc7, 0xefc8, 0xefc9, 0xefcb, 0xefe3, 0xefe5, 0xf014, 0xf015,
    0xf017, 0xf019, 0xf030, 0xf037, 0xf03a, 0xf043, 0xf06c, 0xf074,
    0xf0ab, 0xf13b, 0xf190, 0xf191, 0xf192, 0xf193, 0xf194, 0xf1d7,
    0xf1e3, 0xf23d, 0xf254, 0xf4aa, 0xf712, 0xf7f2
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 63475, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 62, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    61, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    49, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] = {
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 2, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 9, 0, 5, -4, 0, 0,
    0, 0, -11, -12, 1, 9, 4, 3,
    -8, 1, 9, 1, 8, 2, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 12, 2, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 4, 0, -6, 0, 0, 0, 0,
    0, -4, 3, 4, 0, 0, -2, 0,
    -1, 2, 0, -2, 0, -2, -1, -4,
    0, 0, 0, 0, -2, 0, 0, -2,
    -3, 0, 0, -2, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    -2, 0, -3, 0, -5, 0, -23, 0,
    0, -4, 0, 4, 6, 0, 0, -4,
    2, 2, 6, 4, -3, 4, 0, 0,
    -11, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -5, -2, -9, 0, -8,
    -1, 0, 0, 0, 0, 0, 7, 0,
    -6, -2, -1, 1, 0, -3, 0, 0,
    -1, -14, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -15, -2, 7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 6,
    0, 2, 0, 0, -4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 7, 2,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 1,
    4, 2, 6, -2, 0, 0, 4, -2,
    -6, -26, 1, 5, 4, 0, -2, 0,
    7, 0, 6, 0, 6, 0, -18, 0,
    -2, 6, 0, 6, -2, 4, 2, 0,
    0, 1, -2, 0, 0, -3, 15, 0,
    15, 0, 6, 0, 8, 2, 3, 6,
    0, 0, 0, -7, 0, 0, 0, 0,
    1, -1, 0, 1, -3, -2, -4, 1,
    0, -2, 0, 0, 0, -8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, -11, 0, -12, 0, 0, 0,
    0, -1, 0, 19, -2, -2, 2, 2,
    -2, 0, -2, 2, 0, 0, -10, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -19, 0, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, 12, 0, 0, -7, 0,
    6, 0, -13, -19, -13, -4, 6, 0,
    0, -13, 0, 2, -4, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 5, 6, -23, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 9, 0, 1, 0, 0, 0,
    0, 0, 1, 1, -2, -4, 0, -1,
    -1, -2, 0, 0, -1, 0, 0, 0,
    -4, 0, -2, 0, -4, -4, 0, -5,
    -6, -6, -4, 0, -4, 0, -4, 0,
    0, 0, 0, -2, 0, 0, 2, 0,
    1, -2, 0, 1, 0, 0, 0, 2,
    -1, 0, 0, 0, -1, 2, 2, -1,
    0, 0, 0, -4, 0, -1, 0, 0,
    0, 0, 0, 1, 0, 2, -1, 0,
    -2, 0, -3, 0, 0, -1, 0, 6,
    0, 0, -2, 0, 0, 0, 0, 0,
    -1, 1, -1, -1, 0, 0, -2, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, -1, 0, -2, -2, 0,
    0, 0, 0, 0, 1, 0, 0, -1,
    0, -2, -2, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, 0, -1, -2, 0, -3, 0, -6,
    -1, -6, 4, 0, 0, -4, 2, 4,
    5, 0, -5, -1, -2, 0, -1, -9,
    2, -1, 1, -10, 2, 0, 0, 1,
    -10, 0, -10, -2, -17, -1, 0, -10,
    0, 4, 5, 0, 2, 0, 0, 0,
    0, 0, 0, -3, -2, 0, -6, 0,
    0, 0, -2, 0, 0, 0, -2, 0,
    0, 0, 0, 0, -1, -1, 0, -1,
    -2, 0, 0, 0, 0, 0, 0, 0,
    -2, -2, 0, -1, -2, -2, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, -2, 0, -2,
    0, -1, 0, -4, 2, 0, 0, -2,
    1, 2, 2, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 0, -2, 0, -2, -1, -2, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    -2, 0, 0, 0, 0, -2, -3, 0,
    -4, 0, 6, -1, 1, -6, 0, 0,
    5, -10, -10, -8, -4, 2, 0, -2,
    -12, -3, 0, -3, 0, -4, 3, -3,
    -12, 0, -5, 0, 0, 1, -1, 2,
    -1, 0, 2, 0, -6, -7, 0, -10,
    -5, -4, -5, -6, -2, -5, 0, -4,
    -5, 1, 0, 1, 0, -2, 0, 0,
    0, 1, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, -1, 0, -1, -2, 0, -3, -4,
    -4, -1, 0, -6, 0, 0, 0, 0,
    0, 0, -2, 0, 0, 0, 0, 1,
    -1, 0, 0, 0, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 9, 0, 0,
    0, 0, 0, 0, 1, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    -4, 0, 0, 0, 0, -10, -6, 0,
    0, 0, -3, -10, 0, 0, -2, 2,
    0, -5, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 2, 0, -3, 0,
    0, 0, 0, 2, 0, 1, -4, -4,
    0, -2, -2, -2, 0, 0, 0, 0,
    0, 0, -6, 0, -2, 0, -3, -2,
    0, -4, -5, -6, -2, 0, -4, 0,
    -6, 0, 0, 0, 0, 15, 0, 0,
    1, 0, 0, -2, 0, 2, 0, -8,
    0, 0, 0, 0, 0, -18, -3, 6,
    6, -2, -8, 0, 2, -3, 0, -10,
    -1, -2, 2, -13, -2, 2, 0, 3,
    -7, -3, -7, -6, -8, 0, 0, -12,
    0, 11, 0, 0, -1, 0, 0, 0,
    -1, -1, -2, -5, -6, 0, -18, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, -1, -2, -3, 0, 0,
    -4, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -4, 0, 0, 4,
    -1, 2, 0, -4, 2, -1, -1, -5,
    -2, 0, -2, -2, -1, 0, -3, -3,
    0, 0, -2, -1, -1, -3, -2, 0,
    0, -2, 0, 2, -1, 0, -4, 0,
    0, 0, -4, 0, -3, 0, -3, -3,
    2, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 2, 0, -3, 0, -1, -2,
    -6, -1, -1, -1, -1, -1, -2, -1,
    0, 0, 0, 0, 0, -2, -2, -2,
    0, 0, 0, 0, 2, -1, 0, -1,
    0, 0, 0, -1, -2, -1, -2, -2,
    -2, 0, 2, 8, -1, 0, -5, 0,
    -1, 4, 0, -2, -8, -2, 3, 0,
    0, -9, -3, 2, -3, 1, 0, -1,
    -2, -6, 0, -3, 1, 0, 0, -3,
    0, 0, 0, 2, 2, -4, -4, 0,
    -3, -2, -3, -2, -2, 0, -3, 1,
    -4, -3, 6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, -2,
    0, 0, -2, -2, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, -1, 0, 0,
    0, 0, 0, -1, 0, 0, 0, 0,
    -3, 0, -4, 0, 0, 0, -6, 0,
    1, -4, 4, 0, -1, -9, 0, 0,
    -4, -2, 0, -8, -5, -5, 0, 0,
    -8, -2, -8, -7, -9, 0, -5, 0,
    2, 13, -2, 0, -4, -2, -1, -2,
    -3, -5, -3, -7, -8, -4, -2, 0,
    0, -1, 0, 1, 0, 0, -13, -2,
    6, 4, -4, -7, 0, 1, -6, 0,
    -10, -1, -2, 4, -18, -2, 1, 0,
    0, -12, -2, -10, -2, -14, 0, 0,
    -13, 0, 11, 1, 0, -1, 0, 0,
    0, 0, -1, -1, -7, -1, 0, -12,
    0, 0, 0, 0, -6, 0, -2, 0,
    -1, -5, -9, 0, 0, -1, -3, -6,
    -2, 0, -1, 0, 0, 0, 0, -9,
    -2, -6, -6, -2, -3, -5, -2, -3,
    0, -4, -2, -6, -3, 0, -2, -4,
    -2, -4, 0, 1, 0, -1, -6, 0,
    4, 0, -3, 0, 0, 0, 0, 2,
    0, 1, -4, 8, 0, -2, -2, -2,
    0, 0, 0, 0, 0, 0, -6, 0,
    -2, 0, -3, -2, 0, -4, -5, -6,
    -2, 0, -4, 2, 8, 0, 0, 0,
    0, 15, 0, 0, 1, 0, 0, -2,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, -4, 0, 0, 0, 0, 0, -1,
    0, 0, 0, -2, -2, 0, 0, -4,
    -2, 0, 0, -4, 0, 3, -1, 0,
    0, 0, 0, 0, 0, 1, 0, 0,
    0, 0, 3, 4, 2, -2, 0, -6,
    -3, 0, 6, -6, -6, -4, -4, 8,
    3, 2, -17, -1, 4, -2, 0, -2,
    2, -2, -7, 0, -2, 2, -2, -2,
    -6, -2, 0, 0, 6, 4, 0, -5,
    0, -11, -2, 6, -2, -7, 1, -2,
    -6, -6, -2, 8, 2, 0, -3, 0,
    -5, 0, 2, 6, -4, -7, -8, -5,
    6, 0, 1, -14, -2, 2, -3, -1,
    -4, 0, -4, -7, -3, -3, -2, 0,
    0, -4, -4, -2, 0, 6, 4, -2,
    -11, 0, -11, -3, 0, -7, -11, -1,
    -6, -3, -6, -5, 5, 0, 0, -2,
    0, -4, -2, 0, -2, -3, 0, 3,
    -6, 2, 0, 0, -10, 0, -2, -4,
    -3, -1, -6, -5, -6, -4, 0, -6,
    -2, -4, -4, -6, -2, 0, 0, 1,
    9, -3, 0, -6, -2, 0, -2, -4,
    -4, -5, -5, -7, -2, -4, 4, 0,
    -3, 0, -10, -2, 1, 4, -6, -7,
    -4, -6, 6, -2, 1, -18, -3, 4,
    -4, -3, -7, 0, -6, -8, -2, -2,
    -2, -2, -4, -6, -1, 0, 0, 6,
    5, -1, -12, 0, -12, -4, 5, -7,
    -13, -4, -7, -8, -10, -6, 4, 0,
    0, 0, 0, -2, 0, 0, 2, -2,
    4, 1, -4, 4, 0, 0, -6, -1,
    0, -1, 0, 1, 1, -2, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 2, 6, 0, 0, -2, 0, 0,
    0, 0, -1, -1, -2, 0, 0, 0,
    1, 2, 0, 0, 0, 0, 2, 0,
    -2, 0, 7, 0, 3, 1, 1, -2,
    0, 4, 0, 0, 0, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 6, 0, 5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -12, 0, -2, 3, 0, 6,
    0, 0, 19, 2, -4, -4, 2, 2,
    -1, 1, -10, 0, 0, 9, -12, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -13, 7, 27, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, -4,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, -5, 0,
    0, 1, 0, 0, 2, 25, -4, -2,
    6, 5, -5, 2, 0, 0, 2, 2,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -25, 5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, 0, 0, -5, 0, 0, 0, 0,
    -4, -1, 0, 0, 0, -4, 0, -2,
    0, -9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -13, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, -2, 0, 0, -4, 0, -3, 0,
    -5, 0, 0, 0, -3, 2, -2, 0,
    0, -5, -2, -4, 0, 0, -5, 0,
    -2, 0, -9, 0, -2, 0, 0, -16,
    -4, -8, -2, -7, 0, 0, -13, 0,
    -5, -1, 0, 0, 0, 0, 0, 0,
    0, 0, -3, -3, -2, -3, 0, 0,
    0, 0, -4, 0, -4, 2, -2, 4,
    0, -1, -4, -1, -3, -4, 0, -2,
    -1, -1, 1, -5, -1, 0, 0, 0,
    -17, -2, -3, 0, -4, 0, -1, -9,
    -2, 0, 0, -1, -2, 0, 0, 0,
    0, 1, 0, -1, -3, -1, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, -4, 0, -1, 0, 0, 0, -4,
    2, 0, 0, 0, -5, -2, -4, 0,
    0, -5, 0, -2, 0, -9, 0, 0,
    0, 0, -19, 0, -4, -7, -10, 0,
    0, -13, 0, -1, -3, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -3, -1,
    -3, 1, 0, 0, 3, -2, 0, 6,
    9, -2, -2, -6, 2, 9, 3, 4,
    -5, 2, 8, 2, 6, 4, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 12, 9, -3, -2, 0, -2,
    15, 8, 15, 0, 0, 0, 2, 0,
    0, 7, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 3,
    0, 0, 0, 0, -16, -2, -2, -8,
    -9, 0, 0, -13, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    3, 0, 0, 0, 0, -16, -2, -2,
    -8, -9, 0, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, -4, 2, 0, -2,
    2, 3, 2, -6, 0, 0, -2, 2,
    0, 2, 0, 0, 0, 0, -5, 0,
    -2, -1, -4, 0, -2, -8, 0, 12,
    -2, 0, -4, -1, 0, -1, -3, 0,
    -2, -5, -4, -2, 0, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 0, 0, 0, -16,
    -2, -2, -8, -9, 0, 0, -13, 0,
    0, 0, 0, 0, 0, 10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, -6, -2, -2, 6, -2, -2,
    -8, 1, -1, 1, -1, -5, 0, 4,
    0, 2, 1, 2, -5, -8, -2, 0,
    -7, -4, -5, -8, -7, 0, -3, -4,
    -2, -2, -2, -1, -2, -1, 0, -1,
    -1, 3, 0, 3, -1, 0, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, -2, -2, 0, 0,
    -5, 0, -1, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -12, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, -2, 0, -2,
    0, 0, 0, 0, -2, 0, 0, -3,
    -2, 2, 0, -3, -4, -1, 0, -6,
    -1, -4, -1, -2, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -13, 0, 6, 0, 0, -3, 0,
    0, 0, 0, -2, 0, -2, 0, 0,
    -1, 0, 0, -1, 0, -4, 0, 0,
    8, -2, -6, -6, 1, 2, 2, 0,
    -5, 1, 3, 1, 6, 1, 6, -1,
    -5, 0, 0, -8, 0, 0, -6, -5,
    0, 0, -4, 0, -2, -3, 0, -3,
    0, -3, 0, -1, 3, 0, -2, -6,
    -2, 7, 0, 0, -2, 0, -4, 0,
    0, 2, -4, 0, 2, -2, 2, 0,
    0, -6, 0, -1, -1, 0, -2, 2,
    -2, 0, 0, 0, -8, -2, -4, 0,
    -6, 0, 0, -9, 0, 7, -2, 0,
    -3, 0, 1, 0, -2, 0, -2, -6,
    0, -2, 2, 0, 0, 0, 0, -1,
    0, 0, 2, -2, 1, 0, 0, -2,
    -1, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -12, 0, 4, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    -2, -2, 0, 0, 0, 4, 0, 4,
    0, 0, 0, 0, 0, -12, -11, 1,
    8, 6, 3, -8, 1, 8, 0, 7,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes = {
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 61,
    .right_class_cnt     = 49,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_montserrat_12_subpx = {
#else
lv_font_t lv_font_montserrat_12_subpx = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 15,          /*The maximum line height required by the font*/
    .base_line = 3,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_HOR,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if LV_FONT_MONTSERRAT_12_SUBPX*/

