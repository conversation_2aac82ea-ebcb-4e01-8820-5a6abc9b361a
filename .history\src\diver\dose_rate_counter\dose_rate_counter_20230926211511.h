#ifndef __DOSE_RATE_COUNTER_H__
#define __DOSE_RATE_COUNTER_H__

#include <stdint.h> 
#include "../jump_averager/jump_averager.h"

/**
 * @brief 使用说明：
 * step 1: 定义一个 drc_sensor_obj_t 对象
 * step 2: 给对象中必要成员赋值：
 * 赋值对象中的 API 回调函数
 * 填充对象中的 fitting_param 拟合参数
 * 赋值所需的报警阈值（不需要报警的阈值赋值为0）
 * step 3: 调用 drc_set_jpavg_param() 函数，初始化算法参数
 * step 4: 将 drc_pulse_interrupt_handler() 在平台的脉冲中断函数中调用
 * step 5: 将 drc_second_interrupt_handler() 在平台的秒中断函数中调用
 * step 6: drc_sensor_switch() 函数，开、关采集
 * 结果获取：直接访问对象中的 cps、dose_rate、dose_sum...等所需的量
 */


//回调API结构体
typedef struct
{
    void (*pin_init)(void);//引脚初始化回调
    void (*sensor_pw_switch)(uint8_t new_state);//传感器电源开关回调：0关闭；1开启
    void (*second_interrupt_init)(uint8_t enable);//1秒中断初始化回调。enable：0关闭；1开启

}drc_sensor_api_t;

#define MAX_FITI_PARAM_LEN 8    //最多接受几个分段拟合参数
//剂量率分段二次拟合参数结构体
typedef struct 
{
    float cps_start;
    float cps_end;
    float param_a;
    float param_b;
    float param_c;
}piecewise_fitting_param_t;

typedef struct
{
    //需配置或部分配置的量：
    jump_averager_obj_t jump_averager;//跳变检测平均算法的检测器
    drc_sensor_api_t api;//需要用户根据平台实现的API回调函数
    piecewise_fitting_param_t fitting_param[MAX_FITI_PARAM_LEN];
    uint16_t fitting_param_num;
    float alarm_param_max_dose_rate; //报警阈值：剂量率过高阈值
    float alarm_param_min_dose_rate; //报警阈值：剂量率过低阈值
    float alarm_param_max_dose_sum; //报警阈值：总剂量过高阈值
 
    //自动变量：
    uint8_t re_start;//重新开始标志
    uint32_t cnt_temp;
    uint32_t cnt_now;

    //以下是可访问的结果数据：
    uint8_t power_statu; //当前开关状态
    float cps;//计数率
    float dose_rate;//剂量率
    float dose_sum;//总剂量
    /*报警标志位：
    相应bit置1表示报警状态发生：
    bit0 -> 剂量率过低报警位；
    bit1 -> 剂量率过高报警位；
    bit2 -> 总剂量过高报警位；
    */
    uint8_t alarm;
}drc_sensor_obj_t;

void drc_sensor_switch(drc_sensor_obj_t * obj, uint8_t switch_t);
void drc_set_jpavg_param(drc_sensor_obj_t * obj, float cgm1, 
float cgm2, uint32_t out_cgm1_nt, uint32_t continue_change_nt, uint32_t min_data_num);
void drc_pulse_interrupt_handler(drc_sensor_obj_t * obj);
void drc_second_interrupt_handler(drc_sensor_obj_t *obj);

#endif



