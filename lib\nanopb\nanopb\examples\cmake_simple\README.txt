Nanopb example "simple" using CMake
=======================

This example is the same as the simple nanopb example but built using CMake.

Example usage
-------------

On Linux, create a build directory and then call cmake:

    nanopb/examples/cmake_simple$ mkdir build
    nanopb/examples/cmake_simple$ cd build/
    nanopb/examples/cmake_simple/build$ cmake ..
    nanopb/examples/cmake_simple/build$ make

After that, you can run it with the command: ./simple

On other platforms supported by CMake, refer to CMake instructions.
