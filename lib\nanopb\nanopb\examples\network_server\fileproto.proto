// This defines protocol for a simple server that lists files.
//
// See also the nanopb-specific options in fileproto.options.

syntax = "proto2";

message ListFilesRequest {
    optional string path = 1 [default = "/"];
}

message FileInfo {
    required uint64 inode = 1;
    required string name = 2;
}

message ListFilesResponse {
    optional bool path_error = 1 [default = false];
    repeated FileInfo file = 2;
}

