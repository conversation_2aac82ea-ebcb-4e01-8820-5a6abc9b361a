#include "jump_averager.h"
#include <math.h>
#include <string.h>
#include <stdio.h>

/**
 * @brief 获取数据稳定均值
 * @param obj 目标跳变检测平均器对象
 * @return 当前跳变检测平均器的稳定均值
 */
float JPAVG_GetDataStableAverage(jump_averager_obj_t * obj)
{
    return obj->stable_data_average;
}

/**
 * @brief 获取数据当前均方差
 * @param obj 目标跳变检测平均器对象
 * @return 当前跳变检测平均器的稳定均方差
 */
float JPAVG_GetDataStableAverageVariance(jump_averager_obj_t * obj)
{
    return obj->stable_data_average_variance;
}

/**
 * @brief 拷贝原始数据缓存
 * @param obj 目标跳变检测平均器对象
 * @param out_buffer 数据输出指针
 * @param offset 数据起始偏移
 * @param copy_length 需要拷贝的长度
 * @return 成功返回0
 */
uint8_t JPAVG_CopyRawData(jump_averager_obj_t * obj, DATA_TYPE * out_buffer, uint32_t offset, uint32_t copy_length)
{
    if(copy_length > (DATA_BUF_LEN - offset))
    {
        return 1;
    }

    memcpy(out_buffer, (obj->data_buffer + offset), copy_length);
    return 0;
}

/**
 * @brief 添加原始数据到缓存
 * @param data 本次添加的数据
 * @param obj 添加到哪个检测器对象
 * @return 跳变判断返回值，0表示无跳变，1表示3σ方法宽阈值检测到跳变，2表示3σ方法窄阈值检测到跳变，3表示连续增减法检测到跳变
 */
uint8_t JPAVG_AddDataToBuffer(DATA_TYPE data, jump_averager_obj_t * obj)
{
    if(obj->last_data_buffer_filed_num >= obj->param_out_cgm_1_t)
    {
        /// 将 “最近数组” 中最老的数填入原始数据缓存
        DATA_TYPE save_data = obj->last_data_buffer[obj->last_data_buffer_index];//获取 “最近数组” 中最老的数据
        obj->data_buffer_sum -= obj->data_buffer[obj->data_buffer_index];
        obj->data_buffer[obj->data_buffer_index] = save_data;
        obj->data_buffer_sum += obj->data_buffer[obj->data_buffer_index];
        obj->data_buffer_index ++;
        obj->data_buffer_index %= DATA_BUF_LEN;
        if(obj->data_buffer_filed_num < DATA_BUF_LEN)
        {
            obj->data_buffer_filed_num ++;
        }

        /// 计算原始数据缓存的所有均值
        obj->stable_data_average = obj->data_buffer_sum / obj->data_buffer_filed_num;

        /// 计算原始数据缓存数据的方差
        obj->data_variance_buffer_sum -= obj->data_variance_buffer[obj->data_variance_buffer_index];
        obj->data_variance_buffer[obj->data_variance_buffer_index] = pow((save_data - obj->stable_data_average), 2);//计算方差
        obj->data_variance_buffer_sum += obj->data_variance_buffer[obj->data_variance_buffer_index];
        obj->data_variance_buffer_index ++;
        obj->data_variance_buffer_index %= DATA_BUF_LEN;
        if(obj->data_variance_buffer_filed_num < DATA_BUF_LEN)
        {
            obj->data_variance_buffer_filed_num ++;
        }

        /// 计算原始数据缓存数据的均方差
        obj->stable_data_average_variance = sqrt(obj->data_variance_buffer_sum / obj->data_variance_buffer_filed_num);     

        if(obj->data_buffer_filed_num >= obj->jump_check_start_data_num)//数据量足以运行跳变检测算法
        {
            double difference = fabs(data - obj->stable_data_average);//本次数据对比之前平均值的差量
            double n_cgm_th_1 = obj->param_cgm_1 * obj->stable_data_average_variance;//nσ阈值
            double n_cgm_th_2 = obj->param_cgm_2 * obj->stable_data_average_variance;//nσ阈值

            //printf("cn:%0.2f, avl:%0.2f, var:%0.2f, dif:%0.2f, ncgm1:%0.2f, ncgm2:%0.2f\r\n",data, obj->stable_data_average, obj->stable_data_average_variance, difference, n_cgm_th_1, n_cgm_th_2);
            if(difference == 0)
            {
                obj->out_difference_times = 0;
            }
            else if(difference >= n_cgm_th_2)
            {
                obj->jump_flag = 1;
            }
            else if(difference >= n_cgm_th_1)//nσ判断：当前数据与稳定数据的差的绝对值 大于 n倍稳定均方差 (落于正态分布n倍均方差区域以外)
            {
                obj->out_difference_times ++;
                if(obj->out_difference_times >= obj->param_out_cgm_1_t)//连续n次落于区域以外
                {
                    obj->jump_flag = 2;
                }
            }
            else//认为仍然处于稳定状态，更新相关变量
            {
                obj->out_difference_times = 0;
            }

            /*---- 第二检测：持续增减法（慢速检测） ----*/   
            if((obj->stable_data_average - (obj->last_data_average)) > 0.001f)
            {
                obj->increase_times ++;
                obj->decrease_times = 0;
            }
            else if((obj->stable_data_average - (obj->last_data_average)) < -0.001f)
            {
                obj->increase_times = 0;
                obj->decrease_times ++;
            }
            else
            {
                obj->increase_times = 0;
                obj->decrease_times = 0;
            }  

            if((obj->increase_times >= obj->param_ct_inodc_t) || (obj->decrease_times >= obj->param_ct_inodc_t))//持续n次的增减
            {
                obj->jump_flag = 3;
                obj->stable_data_average = obj->recent_average_buffer[obj->recent_average_buffer_index];
                memset(obj->recent_average_buffer, 0, sizeof(obj->recent_average_buffer));
                obj->recent_average_buffer_index = 0;
            }
        }
        
        obj->last_data_average = obj->stable_data_average;
        obj->recent_average_buffer[obj->recent_average_buffer_index] = obj->last_data_average;
        obj->recent_average_buffer_index ++;
        obj->recent_average_buffer_index %= (obj->jump_check_start_data_num + obj->param_ct_inodc_t);
    }

    /// 填入本次原始数据 到 最近数组
    obj->last_data_buffer[obj->last_data_buffer_index] = data;
    obj->last_data_buffer_index ++;
    obj->last_data_buffer_index %= obj->param_out_cgm_1_t;
    if(obj->last_data_buffer_filed_num < obj->param_out_cgm_1_t)
    {
        obj->last_data_buffer_filed_num ++;
    }

    return obj->jump_flag;
}

/**
 * @brief 清空数据缓存，重新采集
 * @param obj 目标跳变检测平均器对象
 */
void JPAVG_DataBufferClean(jump_averager_obj_t * obj)
{
    memset(obj->data_buffer, 0, sizeof(obj->data_buffer));
    obj->data_buffer_index = 0;
    obj->data_buffer_filed_num = 0;
    obj->data_buffer_sum = 0;

    memset(obj->last_data_buffer, 0, sizeof(obj->last_data_buffer));
    obj->last_data_buffer_filed_num = 0;
    obj->last_data_buffer_index = 0;

    memset(obj->data_variance_buffer, 0, sizeof(obj->data_variance_buffer));
    obj->data_variance_buffer_index = 0;
    obj->data_variance_buffer_filed_num = 0;
    obj->data_variance_buffer_sum = 0;

    obj->out_difference_times = 0;
    //obj->stable_data_average = 0;
    //obj->stable_data_average_variance = 0;

    obj->last_data_average = 0;
    obj->increase_times = 0;
    obj->decrease_times = 0;
    obj->jump_flag = 0;
}

/**
 * @brief 获取稳定数据缓存已经填充的数据个数
 * @param obj 查询的对象
 */
uint32_t JPAVG_GetStableFiledNum(jump_averager_obj_t * obj)
{
    return obj->data_variance_buffer_filed_num;
}


/*
#define SAMPLE_LENGTH 1000

float stddev(float data[], int len);
float mean(float data[], int len);
void thresholding(float y[], int signals[], int lag, float threshold, float influence);


void thresholding(float y[], int signals[], int lag, float threshold, float influence) {
    memset(signals, 0, sizeof(int) * SAMPLE_LENGTH);
    float filteredY[SAMPLE_LENGTH];
    memcpy(filteredY, y, sizeof(float) * SAMPLE_LENGTH);
    float avgFilter[SAMPLE_LENGTH];
    float stdFilter[SAMPLE_LENGTH];

    avgFilter[lag - 1] = mean(y, lag);
    stdFilter[lag - 1] = stddev(y, lag);

    for (int i = lag; i < SAMPLE_LENGTH; i++) {
        if (fabsf(y[i] - avgFilter[i-1]) > threshold * stdFilter[i-1]) {
            if (y[i] > avgFilter[i-1]) {
                signals[i] = 1;
            } else {
                signals[i] = -1;
            }
            filteredY[i] = influence * y[i] + (1 - influence) * filteredY[i-1];
        } else {
            signals[i] = 0;
        }
        avgFilter[i] = mean(filteredY + i-lag, lag);
        stdFilter[i] = stddev(filteredY + i-lag, lag);
    }
}

float mean(float data[], int len) {
    float sum = 0.0, mean = 0.0;

    int i;
    for(i=0; i<len; ++i) {
        sum += data[i];
    }

    mean = sum/len;
    return mean;


}

float stddev(float data[], int len) {
    float the_mean = mean(data, len);
    float standardDeviation = 0.0;

    int i;
    for(i=0; i<len; ++i) {
        standardDeviation += pow(data[i] - the_mean, 2);
    }

    return sqrt(standardDeviation/len);
}

int main() {
    printf("Hello, World!\n");
    int lag = 100;
    float threshold = 5;
    float influence = 0;
    float y[]=  {1,1,1.1,1,0.9,1,1,1.1,1,0.9,1,1.1,1,1,0.9,1,1,1.1,1,1,1,1,1.1,0.9,1,1.1,1,1,0.9,
  ....
1,1.1,1,1,1.1,1,0.8,0.9,1,1.2,0.9,1,1,1.1,1.2,1,1.5,1,3,2,5,3,2,1,1,1,0.9,1,1,3,       2.6,4,3,3.2,2,1,1,0.8,4,4,2,2.5,1,1,1,1.2,1,1.5,1,3,2,5,3,2,1,1,1,0.9,1,1,3,
       2.6,4,3,3.2,2,1,1,0.8,4,4,2,2.5,1,1,1};

    int signal[SAMPLE_LENGTH];

    thresholding(y, signal,  lag, threshold, influence);

    return 0;
}

Hi, this is a comment I composed over a year ago, but didn't have enough points to post... I'm not still 100% familiar with my past observations, but here it goes. If I doesn't make much sense, I will re-test it. The comment was: "I suspect that the current implementation does not take into account the immediately prior value for the average and stddev filters. For example, with lag = 5, for i = 6, the average of [0,4] (inclusive) is used instead of [1,5] (or perhaps [0,5]?). I would suggest changing '(filteredY + i-lag, lag)' to '(filteredY + i-lag + 1, lag)'".
嗨，这是我一年多前撰写的评论，但没有足够的积分可以发布......我仍然不是100%熟悉我过去的观察，但它在这里。如果我没有多大意义，我会重新测试它。评论是：“我怀疑当前的实现没有考虑平均值和 stddev 过滤器的先前值。例如，当 lag = 5 时，对于 i = 6，使用平均值 [0，4]（含）而不是 [1，5]（或者可能是 [0，5]？我建议将“（filteredY + i-lag， lag）”更改为“（filteredY + i-lag + 1， lag）”。
– 
gkatev
 Jul 5, 2020 at 12:03 
In the first line of thresholding function, you should be considering the size of an int. So instead of memset(signals, 0, sizeof(float) * SAMPLE_LENGTH), the correct code is memset(signals, 0, sizeof(int) * SAMPLE_LENGTH).
在 thresholding 函数的第一行中，您应该考虑 int 的大小。所以正确的代码不是 memset(signals, 0, sizeof(float) * SAMPLE_LENGTH) ，而是 memset(signals, 0, sizeof(int) * SAMPLE_LENGTH) 。
*/